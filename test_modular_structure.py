#!/usr/bin/env python3
"""
Test script to verify the modular CryptoForensics structure

This script tests the basic functionality of the refactored CryptoForensics
modules to ensure the architecture is working correctly.
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test that all modules can be imported correctly."""
    print("🔍 Testing module imports...")
    
    try:
        # Test core imports
        from cryptoforensics.core.config import InvestigationConfig, GlobalConfig
        from cryptoforensics.core.investigator import CryptoForensicsInvestigator, TransactionInfo
        print("✅ Core modules imported successfully")
        
        # Test evidence imports
        from cryptoforensics.evidence.collector import EvidenceCollector
        from cryptoforensics.evidence.audit_trail import AuditTrail
        print("✅ Evidence modules imported successfully")
        
        # Test analysis imports
        from cryptoforensics.analysis.pattern_analyzer import PatternAnalyzer
        from cryptoforensics.analysis.risk_assessor import RiskAssessor
        from cryptoforensics.analysis.suspicious_activity import SuspiciousActivityDetector
        print("✅ Analysis modules imported successfully")
        
        # Test utility imports
        from cryptoforensics.utils.validators import AddressValidator, TransactionValidator
        from cryptoforensics.utils.api_client import APIClient
        print("✅ Utility modules imported successfully")
        
        # Test visualization and reporting imports
        from cryptoforensics.visualization.graph_generator import GraphGenerator
        from cryptoforensics.reporting.report_generator import ReportGenerator
        print("✅ Visualization and reporting modules imported successfully")
        
        # Test CLI imports
        from cryptoforensics.cli.interface import main
        print("✅ CLI module imported successfully")
        
        # Test main package import
        import cryptoforensics
        print(f"✅ Main package imported successfully (v{cryptoforensics.__version__})")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during import: {e}")
        return False

def test_configuration():
    """Test configuration management."""
    print("\n⚙️  Testing configuration management...")
    
    try:
        from cryptoforensics.core.config import InvestigationConfig, GlobalConfig
        
        # Test default configuration
        config = InvestigationConfig()
        print(f"✅ Default investigation config created (max_depth: {config.max_depth})")
        
        # Test global configuration
        global_config = GlobalConfig()
        print(f"✅ Global config created (version: {global_config.version})")
        
        # Test configuration validation
        issues = global_config.validate_configuration()
        if issues:
            print(f"⚠️  Configuration issues found: {len(issues)}")
            for issue in issues[:3]:  # Show first 3 issues
                print(f"   - {issue}")
        else:
            print("✅ Configuration validation passed")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_validators():
    """Test input validation."""
    print("\n🔍 Testing input validators...")
    
    try:
        from cryptoforensics.utils.validators import AddressValidator, TransactionValidator
        
        # Test address validator
        addr_validator = AddressValidator()
        
        # Test valid Bitcoin addresses
        test_addresses = [
            ("**********************************", "bitcoin"),  # Genesis block address
            ("******************************************", "bitcoin"),  # Bech32
            ("******************************************", "ethereum"),  # Ethereum
        ]
        
        for address, network in test_addresses:
            result = addr_validator.validate_address(address, network)
            if result.is_valid:
                print(f"✅ {network} address validated: {address[:20]}...")
            else:
                print(f"❌ {network} address validation failed: {result.error_message}")
        
        # Test transaction validator
        tx_validator = TransactionValidator()
        
        # Test valid transaction ID
        test_txid = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890"
        result = tx_validator.validate_transaction_id(test_txid)
        
        if result.is_valid:
            print(f"✅ Transaction ID validated")
        else:
            print(f"❌ Transaction ID validation failed: {result.error_message}")
        
        return True
        
    except Exception as e:
        print(f"❌ Validator test failed: {e}")
        return False

async def test_investigator():
    """Test the main investigator class."""
    print("\n🕵️  Testing investigator initialization...")
    
    try:
        from cryptoforensics.core.investigator import CryptoForensicsInvestigator
        from cryptoforensics.core.config import InvestigationConfig, GlobalConfig
        
        # Create test configuration
        config = InvestigationConfig(
            max_depth=3,
            output_directory="test_output",
            save_reports=False,
            save_visualizations=False,
            save_evidence=False
        )
        
        global_config = GlobalConfig()
        
        # Initialize investigator
        investigator = CryptoForensicsInvestigator(config, global_config)
        print(f"✅ Investigator initialized (ID: {investigator.investigation_id})")
        
        # Test component initialization
        if hasattr(investigator, 'evidence_collector'):
            print("✅ Evidence collector initialized")
        
        if hasattr(investigator, 'audit_trail'):
            print("✅ Audit trail initialized")
        
        if hasattr(investigator, 'pattern_analyzer'):
            print("✅ Pattern analyzer initialized")
        
        # Test cleanup
        investigator.cleanup()
        print("✅ Investigator cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Investigator test failed: {e}")
        return False

def test_evidence_system():
    """Test evidence collection and audit trail."""
    print("\n📋 Testing evidence collection system...")
    
    try:
        from cryptoforensics.evidence.collector import EvidenceCollector
        from cryptoforensics.evidence.audit_trail import AuditTrail
        from cryptoforensics.core.config import InvestigationConfig
        
        config = InvestigationConfig(
            output_directory="test_output",
            save_evidence=False,  # Don't save files during test
            audit_trail_enabled=True
        )
        
        investigation_id = "test-investigation-123"
        
        # Test evidence collector
        evidence_collector = EvidenceCollector(investigation_id, config)
        
        # Create test evidence item
        test_evidence = evidence_collector.create_evidence_item(
            evidence_type="test",
            description="Test evidence item",
            data={"test_key": "test_value", "number": 42}
        )
        
        print(f"✅ Evidence item created (ID: {test_evidence.evidence_id})")
        
        # Verify integrity
        if test_evidence.verify_integrity():
            print("✅ Evidence integrity verified")
        else:
            print("❌ Evidence integrity check failed")
        
        # Test audit trail
        audit_trail = AuditTrail(investigation_id, config)
        
        # Log test event
        event_id = audit_trail.log_event(
            action="test_action",
            details={"test": "data"},
            user_id="test_user"
        )
        
        print(f"✅ Audit event logged (ID: {event_id})")
        
        # Verify chain integrity
        integrity_results = audit_trail.verify_chain_integrity()
        if integrity_results["chain_valid"]:
            print("✅ Audit chain integrity verified")
        else:
            print("❌ Audit chain integrity check failed")
        
        # Cleanup
        audit_trail.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ Evidence system test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 Starting CryptoForensics modular structure tests...\n")
    
    tests = [
        ("Module Imports", test_imports),
        ("Configuration", test_configuration),
        ("Validators", test_validators),
        ("Investigator", test_investigator),
        ("Evidence System", test_evidence_system),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running {test_name} Test")
        print('='*60)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"\n✅ {test_name} test PASSED")
            else:
                print(f"\n❌ {test_name} test FAILED")
                
        except Exception as e:
            print(f"\n❌ {test_name} test FAILED with exception: {e}")
    
    print(f"\n{'='*60}")
    print(f"TEST SUMMARY")
    print('='*60)
    print(f"Tests passed: {passed}/{total}")
    print(f"Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All tests passed! The modular structure is working correctly.")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
