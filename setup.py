"""
Setup script for CryptoForensics - Professional Cryptocurrency Investigation Tool
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="cryptoforensics",
    version="3.0.0",
    author="Cryptocurrency Investigation Team",
    author_email="<EMAIL>",
    description="Professional-grade cryptocurrency forensics and investigation tool",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/cryptoinvestigation/cryptoforensics",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Legal Industry",
        "Intended Audience :: Financial and Insurance Industry",
        "Topic :: Office/Business :: Financial",
        "Topic :: Security",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
            "pre-commit>=3.0.0",
        ],
        "ml": [
            "scikit-learn>=1.3.0",
            "tensorflow>=2.13.0",
            "torch>=2.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "cryptoforensics=cryptoforensics.cli.interface:main",
            "crypto-investigate=cryptoforensics.cli.interface:main",
        ],
    },
    include_package_data=True,
    package_data={
        "cryptoforensics": [
            "templates/*.html",
            "templates/*.json",
            "config/*.yaml",
            "config/*.json",
        ],
    },
    zip_safe=False,
)
