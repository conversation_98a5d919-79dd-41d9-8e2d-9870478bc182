"""
CryptoForensics - Professional Cryptocurrency Investigation Tool

A comprehensive, professional-grade cryptocurrency forensics platform designed for
law enforcement, financial institutions, and compliance professionals.

Features:
- Multi-chain transaction tracing and analysis
- Advanced pattern recognition and suspicious activity detection
- Machine learning-based risk assessment
- Legal-grade evidence collection and chain of custody
- Professional reporting and visualization
- Audit trails meeting legal standards
- Expert witness documentation support

Author: Cryptocurrency Investigation Team
License: MIT
Version: 3.0.0
"""

__version__ = "3.0.0"
__author__ = "Cryptocurrency Investigation Team"
__license__ = "MIT"
__email__ = "<EMAIL>"

# Exception classes (imported first to avoid circular imports)
from .exceptions import (
    CryptoForensicsError,
    ValidationError,
    APIError,
    EvidenceError,
    AnalysisError
)

# Core imports for easy access
from .models import TransactionInfo, InvestigationResult
from .core.investigator import CryptoForensicsInvestigator
from .core.config import InvestigationConfig, GlobalConfig
from .evidence.collector import EvidenceCollector
from .evidence.audit_trail import AuditTrail
from .analysis.pattern_analyzer import PatternAnalyzer
from .analysis.risk_assessor import RiskAssessor
from .analysis.suspicious_activity import SuspiciousActivityDetector
from .analysis.mixer_detection import MixerDetector
from .analysis.chain_analysis import ChainAnalyzer
from .analysis.cross_chain import CrossChainTracker
from .evidence.blockchain_timestamping import BlockchainTimestamper
from .evidence.compliance import ComplianceManager
from .visualization.advanced_3d import Advanced3DVisualizer
from .visualization.realtime_dashboard import RealTimeDashboard
from .visualization.advanced_analytics import AdvancedAnalytics
from .utils.crypto import CryptoUtils
from .utils.time_utils import TimeUtils
from .utils.performance import PerformanceMonitor
from .security.enhanced_security import EnhancedSecurity
from .security.fault_tolerance import FaultToleranceManager
from .investigation.victim_recovery import VictimRecoveryManager
from .investigation.law_enforcement import LawEnforcementIntegration
from .reporting.report_generator import ReportGenerator
from .visualization.graph_generator import GraphGenerator

# Package metadata
__all__ = [
    # Data models
    "TransactionInfo",
    "InvestigationResult",

    # Core classes
    "CryptoForensicsInvestigator",
    "InvestigationConfig",
    "GlobalConfig",

    # Evidence and audit
    "EvidenceCollector",
    "AuditTrail",

    # Analysis modules
    "PatternAnalyzer",
    "RiskAssessor",
    "SuspiciousActivityDetector",
    "MixerDetector",
    "ChainAnalyzer",
    "CrossChainTracker",

    # Enhanced evidence and compliance
    "BlockchainTimestamper",
    "ComplianceManager",

    # Advanced visualization and analytics
    "Advanced3DVisualizer",
    "RealTimeDashboard",
    "AdvancedAnalytics",

    # Enhanced utilities
    "CryptoUtils",
    "TimeUtils",
    "PerformanceMonitor",

    # Security and fault tolerance
    "EnhancedSecurity",
    "FaultToleranceManager",

    # Professional investigation
    "VictimRecoveryManager",
    "LawEnforcementIntegration",

    # Reporting and visualization
    "ReportGenerator",
    "GraphGenerator",

    # Exceptions
    "CryptoForensicsError",
    "ValidationError",
    "APIError",
    "EvidenceError",
    "AnalysisError",

    # Metadata
    "__version__",
    "__author__",
    "__license__",
    "__email__",
]

# Package-level configuration
import logging
import sys
from pathlib import Path

# Set up package-level logging
def setup_package_logging():
    """Set up package-level logging configuration."""
    logger = logging.getLogger(__name__)

    # Only configure if no handlers exist
    if not logger.handlers:
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)

    return logger

# Initialize package logging
_package_logger = setup_package_logging()

# Package initialization message
_package_logger.info(f"CryptoForensics v{__version__} initialized")

# Ensure required directories exist
def _ensure_directories():
    """Ensure required package directories exist."""
    package_dir = Path(__file__).parent
    required_dirs = [
        package_dir / "logs",
        package_dir / "output",
        package_dir / "cache",
    ]

    for directory in required_dirs:
        directory.mkdir(exist_ok=True)

# Initialize directories
_ensure_directories()
