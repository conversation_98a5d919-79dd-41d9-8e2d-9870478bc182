"""
Risk assessment module for CryptoForensics

Provides comprehensive risk scoring and assessment capabilities
for cryptocurrency transactions and addresses.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from ..core.config import InvestigationConfig
from ..models import TransactionInfo
from ..exceptions import AnalysisError

logger = logging.getLogger(__name__)

@dataclass
class RiskFactor:
    """Individual risk factor assessment."""
    factor_name: str
    score: float
    weight: float
    description: str
    evidence: Dict[str, Any]

class RiskAssessor:
    """
    Advanced risk assessment engine for cryptocurrency investigations.

    Provides multi-dimensional risk scoring based on transaction patterns,
    amounts, timing, and behavioral indicators.
    """

    def __init__(self, config: InvestigationConfig):
        """Initialize risk assessor."""
        self.config = config
        self.risk_factors: List[RiskFactor] = []

        logger.info("Risk assessor initialized")

    async def assess_async(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """
        Perform comprehensive risk assessment asynchronously.

        Args:
            transactions: List of transactions to assess

        Returns:
            Risk assessment results
        """
        if not transactions:
            return {"status": "no_data", "risk_level": "UNKNOWN"}

        try:
            # Calculate individual risk factors
            risk_factors = await self._calculate_risk_factors(transactions)

            # Calculate composite risk score
            composite_score = self._calculate_composite_score(risk_factors)

            # Determine risk level
            risk_level = self._determine_risk_level(composite_score)

            return {
                "composite_risk_score": composite_score,
                "risk_level": risk_level,
                "risk_factors": [
                    {
                        "name": factor.factor_name,
                        "score": factor.score,
                        "weight": factor.weight,
                        "description": factor.description,
                        "evidence": factor.evidence
                    }
                    for factor in risk_factors
                ],
                "recommendations": self._generate_recommendations(risk_level, risk_factors),
                "confidence": self._calculate_confidence(risk_factors)
            }

        except Exception as e:
            logger.error(f"Risk assessment failed: {e}")
            raise AnalysisError(f"Risk assessment failed: {e}")

    async def _calculate_risk_factors(self, transactions: List[TransactionInfo]) -> List[RiskFactor]:
        """Calculate individual risk factors."""
        factors = []

        # Amount-based risk factors
        total_amount = sum(tx.amount_btc for tx in transactions)
        if total_amount > self.config.suspicious_amount_threshold:
            factors.append(RiskFactor(
                factor_name="high_value_transactions",
                score=min(10.0, total_amount / self.config.suspicious_amount_threshold),
                weight=0.3,
                description="High total transaction value detected",
                evidence={"total_amount": total_amount, "threshold": self.config.suspicious_amount_threshold}
            ))

        # Volume-based risk factors
        if len(transactions) > 20:
            factors.append(RiskFactor(
                factor_name="high_transaction_volume",
                score=min(10.0, len(transactions) / 10),
                weight=0.2,
                description="High number of transactions",
                evidence={"transaction_count": len(transactions)}
            ))

        # Depth-based risk factors
        max_depth = max(tx.depth for tx in transactions) if transactions else 0
        if max_depth > 5:
            factors.append(RiskFactor(
                factor_name="deep_transaction_chains",
                score=min(10.0, max_depth / 2),
                weight=0.25,
                description="Deep transaction chain detected",
                evidence={"max_depth": max_depth}
            ))

        return factors

    def _calculate_composite_score(self, risk_factors: List[RiskFactor]) -> float:
        """Calculate weighted composite risk score."""
        if not risk_factors:
            return 0.0

        weighted_sum = sum(factor.score * factor.weight for factor in risk_factors)
        total_weight = sum(factor.weight for factor in risk_factors)

        return weighted_sum / total_weight if total_weight > 0 else 0.0

    def _determine_risk_level(self, composite_score: float) -> str:
        """Determine risk level based on composite score."""
        if composite_score >= 8.0:
            return "CRITICAL"
        elif composite_score >= 6.0:
            return "HIGH"
        elif composite_score >= 4.0:
            return "MEDIUM"
        elif composite_score >= 2.0:
            return "LOW"
        else:
            return "MINIMAL"

    def _generate_recommendations(self, risk_level: str, risk_factors: List[RiskFactor]) -> List[str]:
        """Generate risk-based recommendations."""
        recommendations = []

        if risk_level in ["CRITICAL", "HIGH"]:
            recommendations.append("Immediate escalation to law enforcement recommended")
            recommendations.append("Enhanced monitoring required")

        if risk_level in ["HIGH", "MEDIUM"]:
            recommendations.append("Enhanced due diligence required")
            recommendations.append("Consider filing suspicious activity report")

        return recommendations

    def _calculate_confidence(self, risk_factors: List[RiskFactor]) -> float:
        """Calculate confidence in risk assessment."""
        if not risk_factors:
            return 0.0

        # Confidence based on number and quality of risk factors
        factor_count_score = min(1.0, len(risk_factors) / 5)
        average_evidence_quality = 0.8  # Placeholder

        return (factor_count_score + average_evidence_quality) / 2
