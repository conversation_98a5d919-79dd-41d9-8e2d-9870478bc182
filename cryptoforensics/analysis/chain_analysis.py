"""
Advanced blockchain chain analysis for CryptoForensics

Provides sophisticated algorithms for analyzing transaction chains, detecting
complex patterns, and implementing machine learning approaches for enhanced
fund tracking through obfuscation techniques.
"""

import asyncio
import logging
import numpy as np
from typing import Dict, List, Optional, Any, Set, Tuple, Union
from dataclasses import dataclass
from collections import defaultdict, deque
import networkx as nx
from datetime import datetime, timedelta

from ..models import TransactionInfo
from ..core.config import InvestigationConfig
from ..exceptions import AnalysisError
from ..utils.performance import performance_monitor
from ..utils.time_utils import TimeUtils

logger = logging.getLogger(__name__)

@dataclass
class ChainPattern:
    """Detected chain pattern."""
    pattern_type: str
    confidence: float
    description: str
    transactions: List[str]
    addresses: List[str]
    characteristics: Dict[str, Any]
    risk_indicators: List[str]

@dataclass
class PeelChainResult:
    """Result of peel chain analysis."""
    is_peel_chain: bool
    confidence: float
    chain_length: int
    total_amount: float
    peel_ratio: float
    change_addresses: List[str]
    destination_addresses: List[str]
    pattern_strength: float

@dataclass
class ChainAnalysisResult:
    """Comprehensive chain analysis result."""
    patterns_detected: List[ChainPattern]
    peel_chains: List[PeelChainResult]
    clustering_results: Dict[str, Any]
    flow_analysis: Dict[str, Any]
    risk_assessment: Dict[str, Any]
    recommendations: List[str]

class ChainAnalyzer:
    """
    Advanced blockchain chain analysis system.
    
    Provides sophisticated analysis including:
    - Peel chain detection with machine learning patterns
    - Complex multi-hop transaction analysis
    - UTXO set analysis for enhanced fund tracking
    - Advanced clustering algorithms
    - Flow pattern recognition
    """
    
    def __init__(self, config: InvestigationConfig):
        """
        Initialize chain analyzer.
        
        Args:
            config: Investigation configuration
        """
        self.config = config
        self.transaction_graph = nx.DiGraph()
        self.address_clusters = {}
        self.detected_patterns = []
        
        logger.info("Chain analyzer initialized")
    
    @performance_monitor("chain_analysis")
    async def analyze_chains_async(self, transactions: List[TransactionInfo]) -> ChainAnalysisResult:
        """
        Perform comprehensive chain analysis.
        
        Args:
            transactions: List of transactions to analyze
            
        Returns:
            ChainAnalysisResult with detailed findings
        """
        try:
            if not transactions:
                return ChainAnalysisResult(
                    patterns_detected=[],
                    peel_chains=[],
                    clustering_results={},
                    flow_analysis={},
                    risk_assessment={},
                    recommendations=[]
                )
            
            logger.info(f"Starting chain analysis on {len(transactions)} transactions")
            
            # Build transaction graph
            self._build_transaction_graph(transactions)
            
            # Run analysis components concurrently
            analysis_tasks = [
                self._detect_peel_chains_async(transactions),
                self._analyze_flow_patterns_async(transactions),
                self._perform_advanced_clustering_async(transactions),
                self._detect_complex_patterns_async(transactions),
                self._analyze_utxo_patterns_async(transactions)
            ]
            
            results = await asyncio.gather(*analysis_tasks, return_exceptions=True)
            
            # Combine results
            peel_chains = results[0] if not isinstance(results[0], Exception) else []
            flow_analysis = results[1] if not isinstance(results[1], Exception) else {}
            clustering_results = results[2] if not isinstance(results[2], Exception) else {}
            complex_patterns = results[3] if not isinstance(results[3], Exception) else []
            utxo_analysis = results[4] if not isinstance(results[4], Exception) else {}
            
            # Combine all detected patterns
            all_patterns = complex_patterns
            
            # Generate risk assessment
            risk_assessment = self._generate_risk_assessment(
                peel_chains, all_patterns, flow_analysis, clustering_results
            )
            
            # Generate recommendations
            recommendations = self._generate_recommendations(
                peel_chains, all_patterns, risk_assessment
            )
            
            result = ChainAnalysisResult(
                patterns_detected=all_patterns,
                peel_chains=peel_chains,
                clustering_results=clustering_results,
                flow_analysis=flow_analysis,
                risk_assessment=risk_assessment,
                recommendations=recommendations
            )
            
            logger.info(f"Chain analysis completed: {len(all_patterns)} patterns, {len(peel_chains)} peel chains")
            return result
            
        except Exception as e:
            logger.error(f"Chain analysis failed: {e}")
            raise AnalysisError(f"Chain analysis failed: {e}")
    
    def _build_transaction_graph(self, transactions: List[TransactionInfo]) -> None:
        """Build directed graph of transactions."""
        self.transaction_graph.clear()
        
        for tx in transactions:
            # Add nodes
            self.transaction_graph.add_node(tx.from_address, address_type="source")
            self.transaction_graph.add_node(tx.to_address, address_type="destination")
            
            # Add edge with transaction data
            self.transaction_graph.add_edge(
                tx.from_address,
                tx.to_address,
                txid=tx.txid,
                amount=tx.amount_btc,
                timestamp=tx.timestamp,
                depth=tx.depth,
                confirmations=tx.confirmations
            )
    
    async def _detect_peel_chains_async(self, transactions: List[TransactionInfo]) -> List[PeelChainResult]:
        """Detect peel chain patterns with enhanced algorithms."""
        peel_chains = []
        
        # Group transactions by address to find potential chains
        address_transactions = defaultdict(list)
        for tx in transactions:
            address_transactions[tx.from_address].append(tx)
        
        for address, txs in address_transactions.items():
            if len(txs) < 3:  # Need at least 3 transactions for a chain
                continue
            
            # Sort by timestamp
            sorted_txs = sorted(txs, key=lambda x: TimeUtils.parse_timestamp(x.timestamp) or datetime.min)
            
            # Analyze for peel chain characteristics
            peel_result = self._analyze_peel_chain_sequence(sorted_txs)
            
            if peel_result.is_peel_chain:
                peel_chains.append(peel_result)
        
        return peel_chains
    
    def _analyze_peel_chain_sequence(self, transactions: List[TransactionInfo]) -> PeelChainResult:
        """Analyze a sequence of transactions for peel chain patterns."""
        if len(transactions) < 3:
            return PeelChainResult(
                is_peel_chain=False,
                confidence=0.0,
                chain_length=0,
                total_amount=0.0,
                peel_ratio=0.0,
                change_addresses=[],
                destination_addresses=[],
                pattern_strength=0.0
            )
        
        amounts = [tx.amount_btc for tx in transactions]
        total_amount = sum(amounts)
        
        # Check for decreasing amounts pattern
        decreasing_count = 0
        for i in range(1, len(amounts)):
            if amounts[i] < amounts[i-1] * 0.9:  # 10% decrease threshold
                decreasing_count += 1
        
        decreasing_ratio = decreasing_count / (len(amounts) - 1)
        
        # Enhanced peel chain detection with multiple criteria
        peel_indicators = []
        
        # 1. Decreasing amounts
        if decreasing_ratio > 0.6:
            peel_indicators.append("decreasing_amounts")
        
        # 2. Regular timing intervals
        timestamps = [TimeUtils.parse_timestamp(tx.timestamp) for tx in transactions]
        valid_timestamps = [ts for ts in timestamps if ts is not None]
        
        if len(valid_timestamps) > 2:
            intervals = []
            for i in range(1, len(valid_timestamps)):
                interval = (valid_timestamps[i] - valid_timestamps[i-1]).total_seconds()
                intervals.append(interval)
            
            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                std_interval = np.std(intervals)
                cv = std_interval / avg_interval if avg_interval > 0 else float('inf')
                
                if cv < 0.3:  # Regular intervals
                    peel_indicators.append("regular_timing")
        
        # 3. Amount precision patterns
        precision_pattern = self._analyze_amount_precision(amounts)
        if precision_pattern:
            peel_indicators.append("amount_precision")
        
        # 4. Address reuse patterns
        to_addresses = [tx.to_address for tx in transactions]
        unique_addresses = set(to_addresses)
        if len(unique_addresses) < len(to_addresses) * 0.8:
            peel_indicators.append("address_reuse")
        
        # Calculate confidence based on indicators
        confidence = len(peel_indicators) / 4  # Normalize by number of indicators
        is_peel_chain = confidence > 0.5
        
        # Identify change and destination addresses
        change_addresses = []
        destination_addresses = []
        
        if is_peel_chain:
            # In a peel chain, typically the largest amounts go to change addresses
            # and smaller amounts to destination addresses
            median_amount = np.median(amounts)
            for tx in transactions:
                if tx.amount_btc > median_amount:
                    change_addresses.append(tx.to_address)
                else:
                    destination_addresses.append(tx.to_address)
        
        # Calculate peel ratio (ratio of small to large transactions)
        small_txs = [a for a in amounts if a < np.median(amounts)]
        peel_ratio = len(small_txs) / len(amounts) if amounts else 0
        
        return PeelChainResult(
            is_peel_chain=is_peel_chain,
            confidence=confidence,
            chain_length=len(transactions),
            total_amount=total_amount,
            peel_ratio=peel_ratio,
            change_addresses=list(set(change_addresses)),
            destination_addresses=list(set(destination_addresses)),
            pattern_strength=decreasing_ratio
        )
    
    def _analyze_amount_precision(self, amounts: List[float]) -> bool:
        """Analyze amount precision patterns that might indicate automation."""
        if not amounts:
            return False
        
        # Check for consistent decimal places
        decimal_places = []
        for amount in amounts:
            amount_str = f"{amount:.8f}".rstrip('0')
            if '.' in amount_str:
                decimal_places.append(len(amount_str.split('.')[1]))
            else:
                decimal_places.append(0)
        
        # If most amounts have the same precision, it might indicate automation
        if decimal_places:
            most_common_precision = max(set(decimal_places), key=decimal_places.count)
            precision_ratio = decimal_places.count(most_common_precision) / len(decimal_places)
            return precision_ratio > 0.8
        
        return False
    
    async def _analyze_flow_patterns_async(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Analyze fund flow patterns."""
        flow_analysis = {}
        
        # Calculate flow metrics
        total_volume = sum(tx.amount_btc for tx in transactions)
        unique_addresses = len(set(tx.from_address for tx in transactions) | 
                             set(tx.to_address for tx in transactions))
        
        # Analyze flow concentration
        address_volumes = defaultdict(float)
        for tx in transactions:
            address_volumes[tx.from_address] += tx.amount_btc
            address_volumes[tx.to_address] += tx.amount_btc
        
        # Calculate Gini coefficient for flow distribution
        volumes = list(address_volumes.values())
        gini_coefficient = self._calculate_gini_coefficient(volumes)
        
        # Detect flow anomalies
        flow_anomalies = self._detect_flow_anomalies(transactions)
        
        flow_analysis = {
            "total_volume": total_volume,
            "unique_addresses": unique_addresses,
            "average_transaction_size": total_volume / len(transactions) if transactions else 0,
            "flow_concentration": {
                "gini_coefficient": gini_coefficient,
                "top_addresses": dict(sorted(address_volumes.items(), 
                                           key=lambda x: x[1], reverse=True)[:10])
            },
            "flow_anomalies": flow_anomalies,
            "flow_velocity": self._calculate_flow_velocity(transactions)
        }
        
        return flow_analysis
    
    def _calculate_gini_coefficient(self, values: List[float]) -> float:
        """Calculate Gini coefficient for measuring inequality."""
        if not values or len(values) < 2:
            return 0.0
        
        sorted_values = sorted(values)
        n = len(sorted_values)
        cumsum = np.cumsum(sorted_values)
        
        return (n + 1 - 2 * sum((n + 1 - i) * y for i, y in enumerate(cumsum))) / (n * sum(sorted_values))
    
    def _detect_flow_anomalies(self, transactions: List[TransactionInfo]) -> List[Dict[str, Any]]:
        """Detect anomalous flow patterns."""
        anomalies = []
        
        amounts = [tx.amount_btc for tx in transactions]
        if not amounts:
            return anomalies
        
        # Statistical outlier detection
        q1 = np.percentile(amounts, 25)
        q3 = np.percentile(amounts, 75)
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        outliers = [tx for tx in transactions 
                   if tx.amount_btc < lower_bound or tx.amount_btc > upper_bound]
        
        if outliers:
            anomalies.append({
                "type": "amount_outliers",
                "count": len(outliers),
                "transactions": [tx.txid for tx in outliers],
                "description": "Transactions with unusual amounts detected"
            })
        
        # Temporal anomalies
        timestamps = [TimeUtils.parse_timestamp(tx.timestamp) for tx in transactions]
        valid_timestamps = [ts for ts in timestamps if ts is not None]
        
        if len(valid_timestamps) > 2:
            intervals = []
            for i in range(1, len(valid_timestamps)):
                interval = (valid_timestamps[i] - valid_timestamps[i-1]).total_seconds()
                intervals.append(interval)
            
            if intervals:
                avg_interval = np.mean(intervals)
                std_interval = np.std(intervals)
                
                unusual_intervals = [i for i in intervals 
                                   if abs(i - avg_interval) > 2 * std_interval]
                
                if unusual_intervals:
                    anomalies.append({
                        "type": "timing_anomalies",
                        "count": len(unusual_intervals),
                        "description": "Unusual timing patterns detected"
                    })
        
        return anomalies
    
    def _calculate_flow_velocity(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Calculate flow velocity metrics."""
        if not transactions:
            return {}
        
        timestamps = [TimeUtils.parse_timestamp(tx.timestamp) for tx in transactions]
        valid_timestamps = [ts for ts in timestamps if ts is not None]
        
        if len(valid_timestamps) < 2:
            return {"status": "insufficient_data"}
        
        time_span = (max(valid_timestamps) - min(valid_timestamps)).total_seconds()
        total_volume = sum(tx.amount_btc for tx in transactions)
        
        return {
            "volume_per_second": total_volume / time_span if time_span > 0 else 0,
            "transactions_per_hour": len(transactions) / (time_span / 3600) if time_span > 0 else 0,
            "time_span_hours": time_span / 3600,
            "average_transaction_frequency": time_span / len(transactions) if transactions else 0
        }
    
    async def _perform_advanced_clustering_async(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Perform advanced address clustering analysis."""
        # This would implement sophisticated clustering algorithms
        # For now, return basic structure
        return {
            "clustering_method": "advanced_heuristics",
            "clusters_found": 0,
            "clustering_confidence": 0.0,
            "cluster_details": []
        }
    
    async def _detect_complex_patterns_async(self, transactions: List[TransactionInfo]) -> List[ChainPattern]:
        """Detect complex transaction patterns."""
        patterns = []
        
        # Detect circular patterns
        circular_pattern = self._detect_circular_patterns(transactions)
        if circular_pattern:
            patterns.append(circular_pattern)
        
        # Detect fan-out patterns
        fanout_pattern = self._detect_fanout_patterns(transactions)
        if fanout_pattern:
            patterns.append(fanout_pattern)
        
        # Detect consolidation patterns
        consolidation_pattern = self._detect_consolidation_patterns(transactions)
        if consolidation_pattern:
            patterns.append(consolidation_pattern)
        
        return patterns
    
    def _detect_circular_patterns(self, transactions: List[TransactionInfo]) -> Optional[ChainPattern]:
        """Detect circular transaction patterns."""
        # Build address graph
        address_graph = defaultdict(set)
        for tx in transactions:
            address_graph[tx.from_address].add(tx.to_address)
        
        # Look for cycles
        cycles = []
        visited = set()
        
        def dfs_cycle(node, path, rec_stack):
            if node in rec_stack:
                cycle_start = path.index(node)
                cycles.append(path[cycle_start:])
                return
            
            if node in visited:
                return
            
            visited.add(node)
            rec_stack.add(node)
            path.append(node)
            
            for neighbor in address_graph.get(node, set()):
                dfs_cycle(neighbor, path.copy(), rec_stack.copy())
            
            rec_stack.remove(node)
        
        for node in address_graph:
            if node not in visited:
                dfs_cycle(node, [], set())
        
        if cycles:
            # Find transactions involved in cycles
            cycle_addresses = set()
            for cycle in cycles:
                cycle_addresses.update(cycle)
            
            cycle_transactions = [tx.txid for tx in transactions 
                                if tx.from_address in cycle_addresses or tx.to_address in cycle_addresses]
            
            return ChainPattern(
                pattern_type="circular",
                confidence=min(1.0, len(cycles) / len(address_graph)),
                description=f"Detected {len(cycles)} circular transaction patterns",
                transactions=cycle_transactions,
                addresses=list(cycle_addresses),
                characteristics={
                    "cycle_count": len(cycles),
                    "average_cycle_length": sum(len(cycle) for cycle in cycles) / len(cycles)
                },
                risk_indicators=["potential_layering", "obfuscation_attempt"]
            )
        
        return None
    
    def _detect_fanout_patterns(self, transactions: List[TransactionInfo]) -> Optional[ChainPattern]:
        """Detect fan-out distribution patterns."""
        # Count outgoing transactions per address
        outgoing_counts = defaultdict(int)
        for tx in transactions:
            outgoing_counts[tx.from_address] += 1
        
        # Find addresses with high fan-out
        high_fanout_addresses = [addr for addr, count in outgoing_counts.items() if count > 5]
        
        if high_fanout_addresses:
            fanout_transactions = [tx.txid for tx in transactions 
                                 if tx.from_address in high_fanout_addresses]
            
            return ChainPattern(
                pattern_type="fanout",
                confidence=len(high_fanout_addresses) / len(outgoing_counts),
                description=f"Detected fan-out pattern from {len(high_fanout_addresses)} addresses",
                transactions=fanout_transactions,
                addresses=high_fanout_addresses,
                characteristics={
                    "high_fanout_addresses": len(high_fanout_addresses),
                    "max_fanout": max(outgoing_counts.values()),
                    "average_fanout": sum(outgoing_counts.values()) / len(outgoing_counts)
                },
                risk_indicators=["potential_distribution", "mixing_behavior"]
            )
        
        return None
    
    def _detect_consolidation_patterns(self, transactions: List[TransactionInfo]) -> Optional[ChainPattern]:
        """Detect consolidation patterns."""
        # Count incoming transactions per address
        incoming_counts = defaultdict(int)
        for tx in transactions:
            incoming_counts[tx.to_address] += 1
        
        # Find addresses with high consolidation
        high_consolidation_addresses = [addr for addr, count in incoming_counts.items() if count > 5]
        
        if high_consolidation_addresses:
            consolidation_transactions = [tx.txid for tx in transactions 
                                        if tx.to_address in high_consolidation_addresses]
            
            return ChainPattern(
                pattern_type="consolidation",
                confidence=len(high_consolidation_addresses) / len(incoming_counts),
                description=f"Detected consolidation pattern to {len(high_consolidation_addresses)} addresses",
                transactions=consolidation_transactions,
                addresses=high_consolidation_addresses,
                characteristics={
                    "high_consolidation_addresses": len(high_consolidation_addresses),
                    "max_consolidation": max(incoming_counts.values()),
                    "average_consolidation": sum(incoming_counts.values()) / len(incoming_counts)
                },
                risk_indicators=["potential_collection", "wallet_consolidation"]
            )
        
        return None
    
    async def _analyze_utxo_patterns_async(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Analyze UTXO patterns for enhanced tracking."""
        # This would implement UTXO set analysis
        # For now, return basic structure
        return {
            "utxo_analysis": "placeholder",
            "unspent_outputs": 0,
            "spending_patterns": {}
        }
    
    def _generate_risk_assessment(self, peel_chains: List[PeelChainResult], 
                                 patterns: List[ChainPattern],
                                 flow_analysis: Dict[str, Any],
                                 clustering_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive risk assessment."""
        risk_score = 0
        risk_factors = []
        
        # Peel chain risk
        if peel_chains:
            high_confidence_peels = [pc for pc in peel_chains if pc.confidence > 0.7]
            if high_confidence_peels:
                risk_score += 5
                risk_factors.append("high_confidence_peel_chains")
        
        # Pattern risk
        high_risk_patterns = [p for p in patterns if "obfuscation" in p.risk_indicators]
        if high_risk_patterns:
            risk_score += 3
            risk_factors.append("obfuscation_patterns")
        
        # Flow concentration risk
        gini = flow_analysis.get("flow_concentration", {}).get("gini_coefficient", 0)
        if gini > 0.8:
            risk_score += 2
            risk_factors.append("high_flow_concentration")
        
        # Determine risk level
        if risk_score >= 8:
            risk_level = "CRITICAL"
        elif risk_score >= 5:
            risk_level = "HIGH"
        elif risk_score >= 3:
            risk_level = "MEDIUM"
        elif risk_score >= 1:
            risk_level = "LOW"
        else:
            risk_level = "MINIMAL"
        
        return {
            "risk_score": risk_score,
            "risk_level": risk_level,
            "risk_factors": risk_factors,
            "peel_chain_count": len(peel_chains),
            "pattern_count": len(patterns),
            "flow_anomalies": len(flow_analysis.get("flow_anomalies", []))
        }
    
    def _generate_recommendations(self, peel_chains: List[PeelChainResult],
                                 patterns: List[ChainPattern],
                                 risk_assessment: Dict[str, Any]) -> List[str]:
        """Generate investigation recommendations."""
        recommendations = []
        
        if peel_chains:
            recommendations.append("Peel chains detected - focus on change address analysis")
            recommendations.append("Investigate timing patterns for user behavior fingerprinting")
        
        if patterns:
            recommendations.append("Complex patterns detected - consider advanced clustering techniques")
            
        risk_level = risk_assessment.get("risk_level", "MINIMAL")
        if risk_level in ["HIGH", "CRITICAL"]:
            recommendations.append("High-risk activity detected - escalate investigation")
            recommendations.append("Consider law enforcement cooperation")
        
        recommendations.append("Apply machine learning clustering for enhanced analysis")
        recommendations.append("Monitor identified addresses for future activity")
        
        return recommendations
