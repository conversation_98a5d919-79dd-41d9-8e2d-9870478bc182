"""
Analysis module for CryptoForensics

Provides advanced pattern recognition, risk assessment, suspicious activity detection,
mixer detection, chain analysis, and cross-chain tracking for cryptocurrency investigations.
"""

from .pattern_analyzer import PatternAnalyzer
from .risk_assessor import RiskAssessor
from .suspicious_activity import SuspiciousActivityDetector
from .mixer_detection import MixerDetector, MixerDetectionResult, MixerSignature
from .chain_analysis import ChainAnalyzer, ChainAnalysisResult, PeelChainResult, ChainPattern
from .cross_chain import CrossChainTracker, CrossChainAnalysisResult, CrossChainEvent, AtomicSwapResult, BridgeTransactionResult

__all__ = [
    # Core analysis
    "PatternAnalyzer",
    "RiskAssessor",
    "SuspiciousActivityDetector",

    # Advanced analysis
    "MixerDetector",
    "MixerDetectionResult",
    "MixerSignature",
    "ChainAnalyzer",
    "ChainAnalysisResult",
    "PeelChainResult",
    "ChainPattern",
    "CrossChainTracker",
    "CrossChainAnalysisResult",
    "CrossChainEvent",
    "AtomicSwapResult",
    "BridgeTransactionResult"
]
