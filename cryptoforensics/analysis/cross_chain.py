"""
Cross-chain tracking and analysis for CryptoForensics

Provides capabilities for tracking funds across different blockchain networks,
detecting atomic swaps, bridge transactions, and cross-chain obfuscation techniques.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass
from collections import defaultdict
from datetime import datetime, timedelta

from ..models import TransactionInfo
from ..core.config import InvestigationConfig
from ..exceptions import AnalysisError
from ..utils.performance import performance_monitor
from ..utils.time_utils import TimeUtils

logger = logging.getLogger(__name__)

@dataclass
class CrossChainEvent:
    """Cross-chain transaction event."""
    event_type: str  # "atomic_swap", "bridge", "wrapped_token", "exchange"
    source_chain: str
    destination_chain: str
    source_address: str
    destination_address: str
    amount: float
    timestamp: str
    confidence: float
    evidence: Dict[str, Any]

@dataclass
class AtomicSwapResult:
    """Result of atomic swap detection."""
    swap_detected: bool
    swap_type: str
    confidence: float
    participants: List[str]
    assets_involved: List[str]
    timelock_evidence: Dict[str, Any]
    hash_evidence: Dict[str, Any]

@dataclass
class BridgeTransactionResult:
    """Result of bridge transaction analysis."""
    bridge_detected: bool
    bridge_protocol: str
    confidence: float
    source_transaction: str
    destination_transaction: Optional[str]
    bridge_addresses: List[str]
    lock_unlock_evidence: Dict[str, Any]

@dataclass
class CrossChainAnalysisResult:
    """Comprehensive cross-chain analysis result."""
    cross_chain_events: List[CrossChainEvent]
    atomic_swaps: List[AtomicSwapResult]
    bridge_transactions: List[BridgeTransactionResult]
    wrapped_tokens: Dict[str, Any]
    exchange_flows: Dict[str, Any]
    risk_assessment: Dict[str, Any]
    recommendations: List[str]

class CrossChainTracker:
    """
    Advanced cross-chain tracking and analysis system.
    
    Provides capabilities for:
    - Atomic swap detection and analysis
    - Bridge transaction identification
    - Wrapped token tracking
    - Cross-chain exchange flow analysis
    - Multi-blockchain correlation
    """
    
    def __init__(self, config: InvestigationConfig):
        """
        Initialize cross-chain tracker.
        
        Args:
            config: Investigation configuration
        """
        self.config = config
        self.known_bridges = self._load_known_bridges()
        self.known_atomic_swap_contracts = self._load_atomic_swap_contracts()
        self.wrapped_token_mappings = self._load_wrapped_token_mappings()
        
        logger.info("Cross-chain tracker initialized")
    
    def _load_known_bridges(self) -> Dict[str, Dict[str, Any]]:
        """Load known bridge protocols and their characteristics."""
        return {
            "polygon_bridge": {
                "source_chains": ["ethereum"],
                "destination_chains": ["polygon"],
                "bridge_addresses": {
                    "ethereum": ["******************************************"],
                    "polygon": ["******************************************"]
                },
                "characteristics": {
                    "lock_unlock": True,
                    "mint_burn": False,
                    "typical_delay": 600  # 10 minutes
                }
            },
            "arbitrum_bridge": {
                "source_chains": ["ethereum"],
                "destination_chains": ["arbitrum"],
                "bridge_addresses": {
                    "ethereum": ["******************************************"],
                    "arbitrum": ["******************************************"]
                },
                "characteristics": {
                    "lock_unlock": True,
                    "mint_burn": True,
                    "typical_delay": 900  # 15 minutes
                }
            },
            "wormhole": {
                "source_chains": ["ethereum", "solana", "terra"],
                "destination_chains": ["ethereum", "solana", "terra", "bsc"],
                "bridge_addresses": {
                    "ethereum": ["******************************************"],
                    "solana": ["worm2ZoG2kUd4vFXhvjh93UUH596ayRfgQ2MgjNMTth"],
                    "bsc": ["******************************************"]
                },
                "characteristics": {
                    "lock_unlock": True,
                    "mint_burn": True,
                    "guardian_network": True,
                    "typical_delay": 300  # 5 minutes
                }
            }
        }
    
    def _load_atomic_swap_contracts(self) -> Dict[str, Dict[str, Any]]:
        """Load known atomic swap contract patterns."""
        return {
            "htlc_bitcoin": {
                "script_pattern": "OP_IF OP_SHA256 <hash> OP_EQUALVERIFY OP_DUP OP_HASH160",
                "timelock_pattern": "OP_CHECKLOCKTIMEVERIFY",
                "characteristics": {
                    "hash_function": "SHA256",
                    "timelock_required": True,
                    "refund_mechanism": True
                }
            },
            "htlc_ethereum": {
                "contract_bytecode_pattern": "6080604052",  # Simplified pattern
                "function_signatures": [
                    "0x7249fbb6",  # withdraw(bytes32)
                    "0x590e1ae3",  # refund()
                    "0x8129fc1c"   # setup(bytes32,uint256)
                ],
                "characteristics": {
                    "hash_function": "KECCAK256",
                    "timelock_required": True,
                    "smart_contract": True
                }
            }
        }
    
    def _load_wrapped_token_mappings(self) -> Dict[str, Dict[str, Any]]:
        """Load wrapped token mappings between chains."""
        return {
            "WBTC": {
                "native_chain": "bitcoin",
                "wrapped_versions": {
                    "ethereum": "******************************************",
                    "polygon": "******************************************",
                    "arbitrum": "******************************************"
                },
                "characteristics": {
                    "custodial": True,
                    "mint_burn": True,
                    "backing_ratio": "1:1"
                }
            },
            "WETH": {
                "native_chain": "ethereum",
                "wrapped_versions": {
                    "polygon": "******************************************",
                    "arbitrum": "******************************************",
                    "optimism": "******************************************"
                },
                "characteristics": {
                    "custodial": False,
                    "mint_burn": True,
                    "backing_ratio": "1:1"
                }
            }
        }
    
    @performance_monitor("cross_chain_analysis")
    async def analyze_cross_chain_async(self, transactions: List[TransactionInfo]) -> CrossChainAnalysisResult:
        """
        Perform comprehensive cross-chain analysis.
        
        Args:
            transactions: List of transactions to analyze
            
        Returns:
            CrossChainAnalysisResult with detailed findings
        """
        try:
            if not transactions:
                return CrossChainAnalysisResult(
                    cross_chain_events=[],
                    atomic_swaps=[],
                    bridge_transactions=[],
                    wrapped_tokens={},
                    exchange_flows={},
                    risk_assessment={},
                    recommendations=[]
                )
            
            logger.info(f"Starting cross-chain analysis on {len(transactions)} transactions")
            
            # Run analysis components concurrently
            analysis_tasks = [
                self._detect_atomic_swaps_async(transactions),
                self._detect_bridge_transactions_async(transactions),
                self._analyze_wrapped_tokens_async(transactions),
                self._analyze_exchange_flows_async(transactions),
                self._detect_cross_chain_patterns_async(transactions)
            ]
            
            results = await asyncio.gather(*analysis_tasks, return_exceptions=True)
            
            # Extract results
            atomic_swaps = results[0] if not isinstance(results[0], Exception) else []
            bridge_transactions = results[1] if not isinstance(results[1], Exception) else []
            wrapped_tokens = results[2] if not isinstance(results[2], Exception) else {}
            exchange_flows = results[3] if not isinstance(results[3], Exception) else {}
            cross_chain_events = results[4] if not isinstance(results[4], Exception) else []
            
            # Generate risk assessment
            risk_assessment = self._generate_cross_chain_risk_assessment(
                atomic_swaps, bridge_transactions, cross_chain_events
            )
            
            # Generate recommendations
            recommendations = self._generate_cross_chain_recommendations(
                atomic_swaps, bridge_transactions, risk_assessment
            )
            
            result = CrossChainAnalysisResult(
                cross_chain_events=cross_chain_events,
                atomic_swaps=atomic_swaps,
                bridge_transactions=bridge_transactions,
                wrapped_tokens=wrapped_tokens,
                exchange_flows=exchange_flows,
                risk_assessment=risk_assessment,
                recommendations=recommendations
            )
            
            logger.info(f"Cross-chain analysis completed: {len(cross_chain_events)} events detected")
            return result
            
        except Exception as e:
            logger.error(f"Cross-chain analysis failed: {e}")
            raise AnalysisError(f"Cross-chain analysis failed: {e}")
    
    async def _detect_atomic_swaps_async(self, transactions: List[TransactionInfo]) -> List[AtomicSwapResult]:
        """Detect atomic swap transactions."""
        atomic_swaps = []
        
        # Group transactions by time windows to find potential swap pairs
        time_windows = defaultdict(list)
        for tx in transactions:
            timestamp = TimeUtils.parse_timestamp(tx.timestamp)
            if timestamp:
                # Group by 1-hour windows
                window = int(timestamp.timestamp() // 3600) * 3600
                time_windows[window].append(tx)
        
        # Analyze each time window for atomic swap patterns
        for window, window_txs in time_windows.items():
            if len(window_txs) < 2:
                continue
            
            # Look for complementary transactions (potential swap pairs)
            swap_result = self._analyze_potential_atomic_swap(window_txs)
            if swap_result.swap_detected:
                atomic_swaps.append(swap_result)
        
        return atomic_swaps
    
    def _analyze_potential_atomic_swap(self, transactions: List[TransactionInfo]) -> AtomicSwapResult:
        """Analyze transactions for atomic swap patterns."""
        # Look for transactions with similar amounts but different directions
        # This is a simplified heuristic - real implementation would be more sophisticated
        
        amounts = [tx.amount_btc for tx in transactions]
        addresses = set(tx.from_address for tx in transactions) | set(tx.to_address for tx in transactions)
        
        # Check for amount symmetry (characteristic of swaps)
        amount_pairs = []
        for i, amount1 in enumerate(amounts):
            for j, amount2 in enumerate(amounts[i+1:], i+1):
                if abs(amount1 - amount2) < 0.001:  # Similar amounts
                    amount_pairs.append((i, j))
        
        swap_detected = len(amount_pairs) > 0 and len(addresses) >= 4  # At least 2 participants
        confidence = len(amount_pairs) / max(len(transactions) / 2, 1) if swap_detected else 0.0
        
        return AtomicSwapResult(
            swap_detected=swap_detected,
            swap_type="htlc" if swap_detected else "none",
            confidence=min(confidence, 1.0),
            participants=list(addresses)[:4] if swap_detected else [],
            assets_involved=["BTC"],  # Simplified - would detect actual assets
            timelock_evidence={
                "timelock_detected": False,  # Would implement actual timelock detection
                "timelock_duration": 0
            },
            hash_evidence={
                "hash_preimage_detected": False,  # Would implement actual hash detection
                "hash_function": "unknown"
            }
        )
    
    async def _detect_bridge_transactions_async(self, transactions: List[TransactionInfo]) -> List[BridgeTransactionResult]:
        """Detect bridge transactions."""
        bridge_transactions = []
        
        # Check transactions against known bridge addresses
        for tx in transactions:
            for bridge_name, bridge_info in self.known_bridges.items():
                bridge_result = self._analyze_bridge_transaction(tx, bridge_name, bridge_info)
                if bridge_result.bridge_detected:
                    bridge_transactions.append(bridge_result)
        
        return bridge_transactions
    
    def _analyze_bridge_transaction(self, transaction: TransactionInfo, 
                                   bridge_name: str, bridge_info: Dict[str, Any]) -> BridgeTransactionResult:
        """Analyze a transaction for bridge patterns."""
        bridge_addresses = []
        for chain, addresses in bridge_info.get("bridge_addresses", {}).items():
            bridge_addresses.extend(addresses)
        
        # Check if transaction involves bridge addresses
        involves_bridge = (transaction.from_address in bridge_addresses or 
                          transaction.to_address in bridge_addresses)
        
        if involves_bridge:
            confidence = 0.9  # High confidence for known bridge addresses
            
            return BridgeTransactionResult(
                bridge_detected=True,
                bridge_protocol=bridge_name,
                confidence=confidence,
                source_transaction=transaction.txid,
                destination_transaction=None,  # Would correlate with destination chain
                bridge_addresses=[addr for addr in bridge_addresses 
                                if addr in [transaction.from_address, transaction.to_address]],
                lock_unlock_evidence={
                    "lock_detected": transaction.to_address in bridge_addresses,
                    "unlock_detected": transaction.from_address in bridge_addresses,
                    "amount": transaction.amount_btc
                }
            )
        
        return BridgeTransactionResult(
            bridge_detected=False,
            bridge_protocol="none",
            confidence=0.0,
            source_transaction=transaction.txid,
            destination_transaction=None,
            bridge_addresses=[],
            lock_unlock_evidence={}
        )
    
    async def _analyze_wrapped_tokens_async(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Analyze wrapped token interactions."""
        wrapped_token_analysis = {
            "wrapped_tokens_detected": [],
            "mint_burn_events": [],
            "cross_chain_correlations": []
        }
        
        # Check transactions against known wrapped token addresses
        for tx in transactions:
            for token_name, token_info in self.wrapped_token_mappings.items():
                wrapped_versions = token_info.get("wrapped_versions", {})
                
                for chain, address in wrapped_versions.items():
                    if tx.from_address == address or tx.to_address == address:
                        wrapped_token_analysis["wrapped_tokens_detected"].append({
                            "token": token_name,
                            "chain": chain,
                            "transaction": tx.txid,
                            "amount": tx.amount_btc,
                            "direction": "mint" if tx.to_address == address else "burn"
                        })
        
        return wrapped_token_analysis
    
    async def _analyze_exchange_flows_async(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Analyze cross-chain exchange flows."""
        # This would implement sophisticated exchange flow analysis
        # For now, return basic structure
        return {
            "exchange_flows_detected": False,
            "potential_exchanges": [],
            "flow_patterns": {}
        }
    
    async def _detect_cross_chain_patterns_async(self, transactions: List[TransactionInfo]) -> List[CrossChainEvent]:
        """Detect general cross-chain patterns and events."""
        cross_chain_events = []
        
        # Analyze timing patterns that might indicate cross-chain activity
        timestamps = [TimeUtils.parse_timestamp(tx.timestamp) for tx in transactions]
        valid_timestamps = [ts for ts in timestamps if ts is not None]
        
        if len(valid_timestamps) > 1:
            # Look for transactions clustered in time (potential cross-chain coordination)
            time_clusters = self._find_time_clusters(valid_timestamps, transactions)
            
            for cluster in time_clusters:
                if len(cluster["transactions"]) >= 2:
                    # Potential cross-chain event
                    event = CrossChainEvent(
                        event_type="coordinated_activity",
                        source_chain="bitcoin",  # Simplified
                        destination_chain="unknown",
                        source_address=cluster["transactions"][0].from_address,
                        destination_address=cluster["transactions"][-1].to_address,
                        amount=sum(tx.amount_btc for tx in cluster["transactions"]),
                        timestamp=cluster["transactions"][0].timestamp,
                        confidence=0.6,  # Medium confidence for timing correlation
                        evidence={
                            "transaction_count": len(cluster["transactions"]),
                            "time_span": cluster["time_span"],
                            "coordination_score": cluster["coordination_score"]
                        }
                    )
                    cross_chain_events.append(event)
        
        return cross_chain_events
    
    def _find_time_clusters(self, timestamps: List[datetime], 
                           transactions: List[TransactionInfo]) -> List[Dict[str, Any]]:
        """Find clusters of transactions in time."""
        clusters = []
        
        if not timestamps:
            return clusters
        
        # Sort transactions by timestamp
        sorted_pairs = sorted(zip(timestamps, transactions), key=lambda x: x[0])
        
        current_cluster = {"transactions": [sorted_pairs[0][1]], "start_time": sorted_pairs[0][0]}
        
        for i in range(1, len(sorted_pairs)):
            timestamp, transaction = sorted_pairs[i]
            
            # If transaction is within 10 minutes of the last one in cluster, add to cluster
            if (timestamp - current_cluster["start_time"]).total_seconds() <= 600:
                current_cluster["transactions"].append(transaction)
            else:
                # Finalize current cluster and start new one
                if len(current_cluster["transactions"]) > 1:
                    current_cluster["time_span"] = (
                        sorted_pairs[i-1][0] - current_cluster["start_time"]
                    ).total_seconds()
                    current_cluster["coordination_score"] = self._calculate_coordination_score(
                        current_cluster["transactions"]
                    )
                    clusters.append(current_cluster)
                
                current_cluster = {"transactions": [transaction], "start_time": timestamp}
        
        # Don't forget the last cluster
        if len(current_cluster["transactions"]) > 1:
            current_cluster["time_span"] = (
                sorted_pairs[-1][0] - current_cluster["start_time"]
            ).total_seconds()
            current_cluster["coordination_score"] = self._calculate_coordination_score(
                current_cluster["transactions"]
            )
            clusters.append(current_cluster)
        
        return clusters
    
    def _calculate_coordination_score(self, transactions: List[TransactionInfo]) -> float:
        """Calculate coordination score for a group of transactions."""
        if len(transactions) < 2:
            return 0.0
        
        # Factors that increase coordination score:
        # 1. Similar amounts
        # 2. Related addresses
        # 3. Regular timing
        
        amounts = [tx.amount_btc for tx in transactions]
        amount_variance = np.var(amounts) if len(amounts) > 1 else 0
        amount_mean = np.mean(amounts)
        
        # Lower variance relative to mean indicates coordination
        amount_score = 1.0 - min(1.0, amount_variance / max(amount_mean, 0.001))
        
        # Address relationship score
        all_addresses = set()
        for tx in transactions:
            all_addresses.add(tx.from_address)
            all_addresses.add(tx.to_address)
        
        # More transactions with fewer unique addresses indicates coordination
        address_score = 1.0 - (len(all_addresses) / (len(transactions) * 2))
        
        # Combined coordination score
        coordination_score = (amount_score + address_score) / 2
        
        return min(1.0, coordination_score)
    
    def _generate_cross_chain_risk_assessment(self, atomic_swaps: List[AtomicSwapResult],
                                             bridge_transactions: List[BridgeTransactionResult],
                                             cross_chain_events: List[CrossChainEvent]) -> Dict[str, Any]:
        """Generate cross-chain risk assessment."""
        risk_score = 0
        risk_factors = []
        
        # Atomic swap risk
        high_confidence_swaps = [swap for swap in atomic_swaps if swap.confidence > 0.7]
        if high_confidence_swaps:
            risk_score += 3
            risk_factors.append("atomic_swaps_detected")
        
        # Bridge transaction risk
        if bridge_transactions:
            risk_score += 2
            risk_factors.append("bridge_transactions_detected")
        
        # Cross-chain coordination risk
        high_confidence_events = [event for event in cross_chain_events if event.confidence > 0.7]
        if high_confidence_events:
            risk_score += 4
            risk_factors.append("cross_chain_coordination")
        
        # Determine risk level
        if risk_score >= 7:
            risk_level = "HIGH"
        elif risk_score >= 4:
            risk_level = "MEDIUM"
        elif risk_score >= 2:
            risk_level = "LOW"
        else:
            risk_level = "MINIMAL"
        
        return {
            "risk_score": risk_score,
            "risk_level": risk_level,
            "risk_factors": risk_factors,
            "atomic_swap_count": len(atomic_swaps),
            "bridge_transaction_count": len(bridge_transactions),
            "cross_chain_event_count": len(cross_chain_events)
        }
    
    def _generate_cross_chain_recommendations(self, atomic_swaps: List[AtomicSwapResult],
                                            bridge_transactions: List[BridgeTransactionResult],
                                            risk_assessment: Dict[str, Any]) -> List[str]:
        """Generate cross-chain investigation recommendations."""
        recommendations = []
        
        if atomic_swaps:
            recommendations.append("Atomic swaps detected - investigate counterparty chains")
            recommendations.append("Analyze timelock patterns for user behavior fingerprinting")
        
        if bridge_transactions:
            recommendations.append("Bridge transactions detected - correlate with destination chains")
            recommendations.append("Contact bridge operators for additional transaction data")
        
        risk_level = risk_assessment.get("risk_level", "MINIMAL")
        if risk_level in ["HIGH", "MEDIUM"]:
            recommendations.append("Cross-chain activity detected - expand investigation scope")
            recommendations.append("Consider multi-blockchain analysis tools")
        
        recommendations.append("Monitor identified addresses across multiple blockchains")
        recommendations.append("Implement cross-chain correlation analysis")
        
        return recommendations
