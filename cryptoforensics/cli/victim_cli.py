"""
Enhanced CLI interface for victim-centric investigation management.

Provides interactive commands for victim information collection, case creation,
investigation management, and victim communication with professional UX.
"""

import asyncio
import click
import logging
import sys
import json
from pathlib import Path
from typing import Optional, Dict, Any, List
from datetime import datetime

from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt, Confirm
from rich import print as rprint

from ..investigation.victim_management import (
    VictimInvestigationManager, VictimProfile, InvestigationCase,
    CaseStatus, CommunicationStatus, DataProtectionLevel, AlertPriority
)
from ..core.config import InvestigationConfig, GlobalConfig
from ..security.enhanced_security import EnhancedSecurity
from ..exceptions import InvestigationError

console = Console()
logger = logging.getLogger(__name__)

class VictimCLI:
    """Professional CLI interface for victim-centric investigations."""

    def __init__(self, config: GlobalConfig):
        """Initialize victim CLI."""
        self.config = config
        self.security = EnhancedSecurity(config.investigation)
        self.victim_manager = VictimInvestigationManager(
            config.investigation,
            self.security
        )

    def display_welcome(self):
        """Display welcome message."""
        welcome_panel = Panel.fit(
            "[bold cyan]CryptoForensics v3.0 - Victim Investigation Management[/bold cyan]\n"
            "[dim]Professional cryptocurrency investigation with victim-centric approach[/dim]",
            border_style="cyan"
        )
        console.print(welcome_panel)
        console.print()

def collect_victim_information() -> Dict[str, Any]:
    """Interactive victim information collection."""
    console.print("\n[bold blue]📋 Victim Information Collection[/bold blue]")
    console.print("[dim]Please provide the following information about the victim:[/dim]\n")

    victim_data = {}

    # Basic Information
    console.print("[bold]Basic Information:[/bold]")
    victim_data["full_name"] = Prompt.ask("Full Name", default="")
    victim_data["email"] = Prompt.ask("Email Address", default="")
    victim_data["phone"] = Prompt.ask("Phone Number (optional)", default="") or None

    # Contact Preferences
    console.print("\n[bold]Contact Preferences:[/bold]")
    contact_methods = ["email", "phone", "secure_message"]
    victim_data["preferred_contact_method"] = Prompt.ask(
        "Preferred Contact Method",
        choices=contact_methods,
        default="email"
    )
    victim_data["timezone"] = Prompt.ask("Timezone", default="UTC")
    victim_data["language"] = Prompt.ask("Language", default="en")

    # Incident Information
    console.print("\n[bold]Incident Information:[/bold]")
    victim_data["incident_description"] = Prompt.ask("Incident Description")
    victim_data["estimated_loss"] = float(Prompt.ask("Estimated Loss Amount", default="0.0"))
    victim_data["currency"] = Prompt.ask("Currency", default="BTC")
    victim_data["incident_date"] = Prompt.ask("Incident Date (YYYY-MM-DD HH:MM:SS)", default="")

    # Affected Addresses
    console.print("\n[bold]Cryptocurrency Addresses:[/bold]")
    addresses = []
    while True:
        address = Prompt.ask("Affected Address (press Enter to finish)", default="")
        if not address:
            break
        addresses.append(address)
    victim_data["affected_addresses"] = addresses

    # GDPR Consent
    console.print("\n[bold]Data Protection:[/bold]")
    victim_data["gdpr_consent"] = Confirm.ask(
        "Do you consent to data processing according to GDPR regulations?"
    )

    return victim_data

def collect_case_information(victim_id: str) -> Dict[str, Any]:
    """Interactive case information collection."""
    console.print(f"\n[bold blue]📁 Case Information for Victim: {victim_id}[/bold blue]")
    console.print("[dim]Please provide case-specific information:[/dim]\n")

    case_data = {}

    # Case Details
    console.print("[bold]Case Details:[/bold]")
    case_data["case_title"] = Prompt.ask("Case Title", default=f"Investigation for {victim_id}")
    case_data["case_description"] = Prompt.ask("Case Description")

    # Priority
    priorities = ["low", "medium", "high", "critical"]
    case_data["case_priority"] = Prompt.ask(
        "Case Priority",
        choices=priorities,
        default="medium"
    )

    # Investigation Parameters
    console.print("\n[bold]Investigation Parameters:[/bold]")
    case_data["investigation_depth"] = int(Prompt.ask("Investigation Depth", default="3"))

    scopes = ["standard", "comprehensive", "targeted", "emergency"]
    case_data["investigation_scope"] = Prompt.ask(
        "Investigation Scope",
        choices=scopes,
        default="standard"
    )

    # Target Addresses
    console.print("\n[bold]Target Addresses for Investigation:[/bold]")
    addresses = []
    while True:
        address = Prompt.ask("Target Address (press Enter to finish)", default="")
        if not address:
            break
        addresses.append(address)
    case_data["target_addresses"] = addresses

    # Assignment
    console.print("\n[bold]Case Assignment:[/bold]")
    case_data["assigned_investigator"] = Prompt.ask("Assigned Investigator", default="unassigned")
    case_data["supervisor"] = Prompt.ask("Supervisor (optional)", default="") or None

    return case_data

@click.group(name="victim")
def victim_cli():
    """Victim-centric investigation management commands."""
    pass

@victim_cli.command()
@click.pass_context
def create_profile(ctx):
    """Create a new victim profile interactively."""
    try:
        cli = VictimCLI(ctx.obj['config'])
        cli.display_welcome()

        console.print("[bold green]🆕 Creating New Victim Profile[/bold green]\n")

        # Collect victim information
        victim_data = collect_victim_information()

        # Create profile
        with console.status("[bold green]Creating victim profile..."):
            profile = asyncio.run(cli.victim_manager.create_victim_profile_async(victim_data))

        # Display success
        success_panel = Panel.fit(
            f"[bold green]✅ Victim Profile Created Successfully[/bold green]\n\n"
            f"[bold]Victim ID:[/bold] {profile.victim_id}\n"
            f"[bold]Name:[/bold] {profile.full_name}\n"
            f"[bold]Email:[/bold] {profile.email}\n"
            f"[bold]Estimated Loss:[/bold] {profile.estimated_loss} {profile.currency}\n"
            f"[bold]Created:[/bold] {profile.created_at.strftime('%Y-%m-%d %H:%M:%S')}",
            border_style="green"
        )
        console.print(success_panel)

        # Ask if user wants to create a case
        if Confirm.ask("\nWould you like to create an investigation case for this victim?"):
            case_data = collect_case_information(profile.victim_id)
            case_data["created_by"] = ctx.obj.get("user", "cli_user")

            with console.status("[bold green]Creating investigation case..."):
                case = asyncio.run(cli.victim_manager.create_investigation_case_async(
                    profile.victim_id, case_data
                ))

            case_panel = Panel.fit(
                f"[bold green]✅ Investigation Case Created[/bold green]\n\n"
                f"[bold]Case ID:[/bold] {case.case_id}\n"
                f"[bold]Title:[/bold] {case.case_title}\n"
                f"[bold]Status:[/bold] {case.case_status.value}\n"
                f"[bold]Priority:[/bold] {case.case_priority.value}\n"
                f"[bold]Folder:[/bold] {case.case_folder_path}",
                border_style="green"
            )
            console.print(case_panel)

    except Exception as e:
        console.print(f"[bold red]❌ Error creating victim profile: {e}[/bold red]")
        sys.exit(1)

@victim_cli.command()
@click.argument('victim_id')
@click.pass_context
def create_case(ctx, victim_id):
    """Create a new investigation case for an existing victim."""
    try:
        cli = VictimCLI(ctx.obj['config'])
        cli.display_welcome()

        console.print(f"[bold green]📁 Creating Investigation Case[/bold green]\n")

        # Verify victim exists
        with console.status("[bold blue]Verifying victim profile..."):
            profile = asyncio.run(cli.victim_manager.get_victim_profile_async(victim_id))

        if not profile:
            console.print(f"[bold red]❌ Victim profile not found: {victim_id}[/bold red]")
            sys.exit(1)

        # Display victim info
        victim_panel = Panel.fit(
            f"[bold]Victim Information:[/bold]\n"
            f"[bold]ID:[/bold] {profile.victim_id}\n"
            f"[bold]Name:[/bold] {profile.full_name}\n"
            f"[bold]Email:[/bold] {profile.email}\n"
            f"[bold]Estimated Loss:[/bold] {profile.estimated_loss} {profile.currency}",
            title="Victim Profile",
            border_style="blue"
        )
        console.print(victim_panel)

        # Collect case information
        case_data = collect_case_information(victim_id)
        case_data["created_by"] = ctx.obj.get("user", "cli_user")

        # Create case
        with console.status("[bold green]Creating investigation case..."):
            case = asyncio.run(cli.victim_manager.create_investigation_case_async(
                victim_id, case_data
            ))

        # Display success
        success_panel = Panel.fit(
            f"[bold green]✅ Investigation Case Created Successfully[/bold green]\n\n"
            f"[bold]Case ID:[/bold] {case.case_id}\n"
            f"[bold]Title:[/bold] {case.case_title}\n"
            f"[bold]Status:[/bold] {case.case_status.value}\n"
            f"[bold]Priority:[/bold] {case.case_priority.value}\n"
            f"[bold]Investigator:[/bold] {case.assigned_investigator}\n"
            f"[bold]Folder:[/bold] {case.case_folder_path}",
            border_style="green"
        )
        console.print(success_panel)

    except Exception as e:
        console.print(f"[bold red]❌ Error creating investigation case: {e}[/bold red]")
        sys.exit(1)

@victim_cli.command()
@click.argument('case_id')
@click.argument('status', type=click.Choice(['intake', 'active', 'analysis', 'reporting', 'completed', 'closed', 'suspended']))
@click.option('--notes', '-n', help='Status update notes')
@click.pass_context
def update_status(ctx, case_id, status, notes):
    """Update investigation case status."""
    try:
        cli = VictimCLI(ctx.obj['config'])

        console.print(f"[bold blue]📊 Updating Case Status[/bold blue]\n")

        # Update status
        with console.status(f"[bold blue]Updating case status to {status}..."):
            asyncio.run(cli.victim_manager.update_case_status_async(
                case_id, CaseStatus(status), notes, ctx.obj.get("user", "cli_user")
            ))

        # Display success
        success_panel = Panel.fit(
            f"[bold green]✅ Case Status Updated Successfully[/bold green]\n\n"
            f"[bold]Case ID:[/bold] {case_id}\n"
            f"[bold]New Status:[/bold] {status.title()}\n"
            f"[bold]Notes:[/bold] {notes or 'None'}\n"
            f"[bold]Updated By:[/bold] {ctx.obj.get('user', 'cli_user')}\n"
            f"[bold]Updated At:[/bold] {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            border_style="green"
        )
        console.print(success_panel)

    except Exception as e:
        console.print(f"[bold red]❌ Error updating case status: {e}[/bold red]")
        sys.exit(1)

@victim_cli.command()
@click.argument('victim_id')
@click.pass_context
def show_profile(ctx, victim_id):
    """Display victim profile information."""
    try:
        cli = VictimCLI(ctx.obj['config'])

        console.print(f"[bold blue]👤 Victim Profile: {victim_id}[/bold blue]\n")

        # Get victim profile
        with console.status("[bold blue]Loading victim profile..."):
            profile = asyncio.run(cli.victim_manager.get_victim_profile_async(victim_id))

        if not profile:
            console.print(f"[bold red]❌ Victim profile not found: {victim_id}[/bold red]")
            sys.exit(1)

        # Display profile information
        profile_table = Table(title="Victim Profile Information", border_style="blue")
        profile_table.add_column("Field", style="bold")
        profile_table.add_column("Value")

        profile_table.add_row("Victim ID", profile.victim_id)
        profile_table.add_row("Full Name", profile.full_name)
        profile_table.add_row("Email", profile.email)
        profile_table.add_row("Phone", profile.phone or "Not provided")
        profile_table.add_row("Preferred Contact", profile.preferred_contact_method)
        profile_table.add_row("Timezone", profile.timezone)
        profile_table.add_row("Language", profile.language)
        profile_table.add_row("Estimated Loss", f"{profile.estimated_loss} {profile.currency}")
        profile_table.add_row("Incident Date", profile.incident_date.strftime('%Y-%m-%d %H:%M:%S') if profile.incident_date else "Not specified")
        profile_table.add_row("Affected Addresses", str(len(profile.affected_addresses)))
        profile_table.add_row("Communication Status", profile.communication_status.value.replace('_', ' ').title())
        profile_table.add_row("Data Protection Level", profile.data_protection_level.value.title())
        profile_table.add_row("Created", profile.created_at.strftime('%Y-%m-%d %H:%M:%S'))
        profile_table.add_row("Last Updated", profile.updated_at.strftime('%Y-%m-%d %H:%M:%S'))

        console.print(profile_table)

        # Show affected addresses if any
        if profile.affected_addresses:
            console.print("\n[bold]Affected Addresses:[/bold]")
            for i, address in enumerate(profile.affected_addresses, 1):
                console.print(f"  {i}. {address}")

        # Show incident description if provided
        if profile.incident_description:
            incident_panel = Panel(
                profile.incident_description,
                title="Incident Description",
                border_style="yellow"
            )
            console.print(f"\n{incident_panel}")

    except Exception as e:
        console.print(f"[bold red]❌ Error displaying victim profile: {e}[/bold red]")
        sys.exit(1)

@victim_cli.command()
@click.argument('case_id')
@click.pass_context
def show_case(ctx, case_id):
    """Display investigation case information."""
    try:
        cli = VictimCLI(ctx.obj['config'])

        console.print(f"[bold blue]📁 Investigation Case: {case_id}[/bold blue]\n")

        # Get case information
        with console.status("[bold blue]Loading case information..."):
            case = asyncio.run(cli.victim_manager.get_investigation_case_async(case_id))

        if not case:
            console.print(f"[bold red]❌ Investigation case not found: {case_id}[/bold red]")
            sys.exit(1)

        # Get case summary
        summary = cli.victim_manager.generate_case_summary(case_id)

        # Display case information
        case_table = Table(title="Investigation Case Information", border_style="blue")
        case_table.add_column("Field", style="bold")
        case_table.add_column("Value")

        case_table.add_row("Case ID", case.case_id)
        case_table.add_row("Victim ID", case.victim_id)
        case_table.add_row("Title", case.case_title)
        case_table.add_row("Status", case.case_status.value.title())
        case_table.add_row("Priority", case.case_priority.value.title())
        case_table.add_row("Progress", f"{case.progress_percentage}%")
        case_table.add_row("Assigned Investigator", case.assigned_investigator)
        case_table.add_row("Supervisor", case.supervisor or "Not assigned")
        case_table.add_row("Investigation Depth", str(case.investigation_depth))
        case_table.add_row("Investigation Scope", case.investigation_scope.title())
        case_table.add_row("Created", case.created_at.strftime('%Y-%m-%d %H:%M:%S'))
        case_table.add_row("Started", case.started_at.strftime('%Y-%m-%d %H:%M:%S') if case.started_at else "Not started")
        case_table.add_row("Completed", case.completed_at.strftime('%Y-%m-%d %H:%M:%S') if case.completed_at else "Not completed")
        case_table.add_row("Case Folder", case.case_folder_path)

        console.print(case_table)

        # Show target addresses if any
        if case.target_addresses:
            console.print("\n[bold]Target Addresses:[/bold]")
            for i, address in enumerate(case.target_addresses, 1):
                console.print(f"  {i}. {address}")

        # Show milestones and next actions
        if case.milestones_completed:
            console.print("\n[bold green]✅ Completed Milestones:[/bold green]")
            for milestone in case.milestones_completed:
                console.print(f"  • {milestone}")

        if case.next_actions:
            console.print("\n[bold yellow]📋 Next Actions:[/bold yellow]")
            for action in case.next_actions:
                console.print(f"  • {action}")

        # Show case description if provided
        if case.case_description:
            description_panel = Panel(
                case.case_description,
                title="Case Description",
                border_style="yellow"
            )
            console.print(f"\n{description_panel}")

    except Exception as e:
        console.print(f"[bold red]❌ Error displaying case information: {e}[/bold red]")
        sys.exit(1)

@victim_cli.command()
@click.argument('victim_id')
@click.pass_context
def list_cases(ctx, victim_id):
    """List all investigation cases for a victim."""
    try:
        cli = VictimCLI(ctx.obj['config'])

        console.print(f"[bold blue]📋 Cases for Victim: {victim_id}[/bold blue]\n")

        # Get victim cases
        with console.status("[bold blue]Loading victim cases..."):
            cases = asyncio.run(cli.victim_manager.list_victim_cases_async(victim_id))

        if not cases:
            console.print(f"[bold yellow]⚠️  No cases found for victim: {victim_id}[/bold yellow]")
            return

        # Display cases table
        cases_table = Table(title=f"Investigation Cases for {victim_id}", border_style="blue")
        cases_table.add_column("Case ID", style="bold")
        cases_table.add_column("Title")
        cases_table.add_column("Status")
        cases_table.add_column("Priority")
        cases_table.add_column("Progress")
        cases_table.add_column("Investigator")
        cases_table.add_column("Created")

        for case in cases:
            status_color = {
                "intake": "yellow",
                "active": "blue",
                "analysis": "cyan",
                "reporting": "magenta",
                "completed": "green",
                "closed": "green",
                "suspended": "red"
            }.get(case.case_status.value, "white")

            priority_color = {
                "low": "green",
                "medium": "yellow",
                "high": "red",
                "critical": "bold red"
            }.get(case.case_priority.value, "white")

            cases_table.add_row(
                case.case_id,
                case.case_title[:30] + "..." if len(case.case_title) > 30 else case.case_title,
                f"[{status_color}]{case.case_status.value.title()}[/{status_color}]",
                f"[{priority_color}]{case.case_priority.value.title()}[/{priority_color}]",
                f"{case.progress_percentage}%",
                case.assigned_investigator,
                case.created_at.strftime('%Y-%m-%d')
            )

        console.print(cases_table)

    except Exception as e:
        console.print(f"[bold red]❌ Error listing victim cases: {e}[/bold red]")
        sys.exit(1)

@victim_cli.command()
@click.argument('victim_id')
@click.argument('case_id')
@click.argument('message')
@click.option('--type', 'update_type', default='progress', help='Update type (progress, status, evidence, etc.)')
@click.pass_context
def send_update(ctx, victim_id, case_id, message, update_type):
    """Send update to victim."""
    try:
        cli = VictimCLI(ctx.obj['config'])

        console.print(f"[bold blue]📧 Sending Update to Victim[/bold blue]\n")

        # Send update
        with console.status("[bold blue]Sending victim update..."):
            record = asyncio.run(cli.victim_manager.send_victim_update_async(
                victim_id, case_id, update_type, message,
                sent_by=ctx.obj.get("user", "cli_user")
            ))

        # Display success
        success_panel = Panel.fit(
            f"[bold green]✅ Update Sent Successfully[/bold green]\n\n"
            f"[bold]Victim ID:[/bold] {victim_id}\n"
            f"[bold]Case ID:[/bold] {case_id}\n"
            f"[bold]Update Type:[/bold] {update_type}\n"
            f"[bold]Communication ID:[/bold] {record.record_id}\n"
            f"[bold]Sent At:[/bold] {record.timestamp.strftime('%Y-%m-%d %H:%M:%S')}",
            border_style="green"
        )
        console.print(success_panel)

    except Exception as e:
        console.print(f"[bold red]❌ Error sending victim update: {e}[/bold red]")
        sys.exit(1)

@victim_cli.command()
@click.argument('case_id')
@click.pass_context
def show_communications(ctx, case_id):
    """Show communication history for a case."""
    try:
        cli = VictimCLI(ctx.obj['config'])

        console.print(f"[bold blue]💬 Communication History: {case_id}[/bold blue]\n")

        # Get communication history
        with console.status("[bold blue]Loading communication history..."):
            communications = asyncio.run(cli.victim_manager.get_case_communication_history_async(case_id))

        if not communications:
            console.print(f"[bold yellow]⚠️  No communications found for case: {case_id}[/bold yellow]")
            return

        # Display communications table
        comm_table = Table(title=f"Communication History for {case_id}", border_style="blue")
        comm_table.add_column("Date/Time", style="bold")
        comm_table.add_column("Type")
        comm_table.add_column("Direction")
        comm_table.add_column("Subject")
        comm_table.add_column("Sent By")
        comm_table.add_column("Status")

        for comm in communications:
            direction_color = "green" if comm.direction == "outbound" else "blue"

            comm_table.add_row(
                comm.timestamp.strftime('%Y-%m-%d %H:%M'),
                comm.communication_type,
                f"[{direction_color}]{comm.direction.title()}[/{direction_color}]",
                comm.subject[:40] + "..." if len(comm.subject) > 40 else comm.subject,
                comm.sent_by,
                comm.delivery_status
            )

        console.print(comm_table)

        # Show recent communication details
        if communications:
            recent = communications[0]
            recent_panel = Panel(
                f"[bold]Subject:[/bold] {recent.subject}\n\n"
                f"{recent.content[:200]}{'...' if len(recent.content) > 200 else ''}",
                title="Most Recent Communication",
                border_style="cyan"
            )
            console.print(f"\n{recent_panel}")

    except Exception as e:
        console.print(f"[bold red]❌ Error showing communications: {e}[/bold red]")
        sys.exit(1)

@victim_cli.command()
@click.pass_context
def dashboard(ctx):
    """Display victim investigation dashboard."""
    try:
        cli = VictimCLI(ctx.obj['config'])
        cli.display_welcome()

        console.print("[bold blue]📊 Victim Investigation Dashboard[/bold blue]\n")

        # This would typically load all active cases, victims, etc.
        # For now, show a placeholder dashboard
        dashboard_panel = Panel.fit(
            "[bold green]🎯 Active Investigations[/bold green]\n"
            "• Use 'victim create-profile' to create new victim profiles\n"
            "• Use 'victim create-case <victim_id>' to create investigation cases\n"
            "• Use 'victim show-profile <victim_id>' to view victim information\n"
            "• Use 'victim show-case <case_id>' to view case details\n"
            "• Use 'victim list-cases <victim_id>' to list all cases for a victim\n"
            "• Use 'victim send-update <victim_id> <case_id> <message>' to send updates\n\n"
            "[bold yellow]📋 Quick Actions[/bold yellow]\n"
            "• Update case status with 'victim update-status <case_id> <status>'\n"
            "• View communications with 'victim show-communications <case_id>'\n"
            "• Access full dashboard with 'victim dashboard'",
            border_style="blue"
        )
        console.print(dashboard_panel)

    except Exception as e:
        console.print(f"[bold red]❌ Error displaying dashboard: {e}[/bold red]")
        sys.exit(1)
