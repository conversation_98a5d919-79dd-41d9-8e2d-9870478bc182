"""
Command-line interface for CryptoForensics

Professional CLI with rich formatting, progress indicators,
and comprehensive investigation management.
"""

import asyncio
import click
import logging
import sys
from pathlib import Path
from typing import Optional

from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich.table import Table
from rich import print as rprint

from ..core.investigator import CryptoForensicsInvestigator
from ..core.config import InvestigationConfig, GlobalConfig
from .. import __version__
from .victim_cli import victim_cli

console = Console()

def print_banner():
    """Print the application banner."""
    banner = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🔍 CryptoForensics v{__version__}                              ║
║                          Professional Cryptocurrency Forensics                ║
╚══════════════════════════════════════════════════════════════════════════════╝

🎯 FEATURES:
   • Advanced transaction tracing and analysis
   • Suspicious activity detection and risk scoring
   • Professional evidence collection and chain of custody
   • Interactive visualizations and comprehensive reporting
   • Audit trail generation for legal proceedings

⚠️  LEGAL NOTICE:
   This tool is for authorized investigations only. Ensure you have proper
   legal authorization before investigating any cryptocurrency addresses or transactions.

═══════════════════════════════════════════════════════════════════════════════
"""
    console.print(banner, style="cyan")

@click.group()
@click.version_option(version=__version__)
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.option('--config', '-c', type=click.Path(exists=True), help='Configuration file path')
@click.pass_context
def cli(ctx, verbose, config):
    """CryptoForensics - Professional Cryptocurrency Investigation Tool"""
    ctx.ensure_object(dict)

    # Set up logging
    log_level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Load configuration
    if config:
        ctx.obj['config'] = GlobalConfig.load_from_file(config)
    else:
        ctx.obj['config'] = GlobalConfig.load_from_env()

    ctx.obj['verbose'] = verbose

@cli.command()
@click.option('--txid', '-t', required=True, help='Initial transaction ID to trace from')
@click.option('--address', '-a', required=True, help='Target address that received the funds')
@click.option('--depth', '-d', default=5, type=click.IntRange(1, 10), help='Maximum tracing depth (1-10)')
@click.option('--output-dir', '-o', default='investigation_results', help='Output directory for results')
@click.option('--no-visualization', is_flag=True, help='Skip generating interactive visualization')
@click.option('--no-evidence', is_flag=True, help='Skip generating evidence package')
@click.pass_context
def investigate(ctx, txid, address, depth, output_dir, no_visualization, no_evidence):
    """Perform a comprehensive cryptocurrency investigation."""

    print_banner()

    # Create investigation configuration
    config = InvestigationConfig(
        max_depth=depth,
        output_directory=output_dir,
        save_visualizations=not no_visualization,
        save_evidence=not no_evidence
    )

    console.print(f"\n🚀 Starting investigation...", style="bold green")
    console.print(f"   TXID: {txid}")
    console.print(f"   Address: {address}")
    console.print(f"   Max Depth: {depth}")
    console.print(f"   Output Directory: {output_dir}")
    console.print("-" * 50)

    try:
        # Run the investigation
        result = asyncio.run(run_investigation_async(config, ctx.obj['config'], txid, address))

        if result:
            console.print(f"\n✅ Investigation completed successfully!", style="bold green")
            display_investigation_results(result)
        else:
            console.print(f"\n⚠️  No transactions found.", style="yellow")
            console.print("This could mean:")
            console.print("   - The funds haven't been moved yet")
            console.print("   - The transaction/address combination is incorrect")
            console.print("   - The funds were moved to non-standard outputs")

    except Exception as e:
        console.print(f"\n❌ Investigation failed: {e}", style="bold red")
        if ctx.obj['verbose']:
            console.print_exception()
        sys.exit(1)

async def run_investigation_async(config: InvestigationConfig,
                                global_config: GlobalConfig,
                                txid: str,
                                address: str):
    """Run investigation asynchronously with progress tracking."""

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:

        # Initialize investigator
        task = progress.add_task("Initializing investigator...", total=None)
        investigator = CryptoForensicsInvestigator(config, global_config)

        try:
            # Perform comprehensive investigation
            progress.update(task, description="Performing comprehensive investigation...")
            result = await investigator.comprehensive_investigation_async(txid, address)

            progress.update(task, description="Generating reports and visualizations...")

            # Generate outputs
            if result.detailed_transactions:
                # Generate report
                report_path = investigator.report_generator.generate_comprehensive_report(
                    result.detailed_transactions,
                    result.advanced_analysis
                )

                # Generate visualization
                if config.save_visualizations:
                    viz_path = investigator.graph_generator.generate_interactive_visualization(
                        result.graph,
                        result.detailed_transactions,
                        result.investigation_id
                    )

                # Export evidence package
                if config.save_evidence:
                    evidence_path = investigator.export_evidence_package()

            progress.update(task, description="Investigation completed!", completed=True)
            return result

        finally:
            investigator.cleanup()

def display_investigation_results(result):
    """Display investigation results in a formatted table."""

    # Create summary table
    table = Table(title="Investigation Summary", show_header=True, header_style="bold magenta")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")

    table.add_row("Investigation ID", result.investigation_id)
    table.add_row("Transactions Found", str(result.basic_results["transaction_count"]))
    table.add_row("Addresses Discovered", str(result.basic_results["address_count"]))
    table.add_row("Total Amount Traced", f"{result.basic_results['total_amount']:.8f} BTC")

    # Add risk assessment if available
    if "risk_assessment" in result.advanced_analysis:
        risk_data = result.advanced_analysis["risk_assessment"]
        table.add_row("Risk Level", risk_data.get("risk_level", "UNKNOWN"))
        table.add_row("Risk Score", f"{risk_data.get('composite_risk_score', 0):.2f}")

    console.print(table)

    # Display key concerns if any
    if result.investigation_summary.get("key_concerns"):
        console.print(f"\n⚠️  Key Concerns Identified:", style="bold yellow")
        for concern in result.investigation_summary["key_concerns"]:
            console.print(f"   • {concern.replace('_', ' ').title()}")

    # Display recommendations
    if "risk_assessment" in result.advanced_analysis:
        recommendations = result.advanced_analysis["risk_assessment"].get("recommendations", [])
        if recommendations:
            console.print(f"\n💡 Recommendations:", style="bold blue")
            for rec in recommendations[:3]:
                console.print(f"   • {rec}")

    # Display output files
    console.print(f"\n📁 Output Files Generated:", style="bold cyan")
    console.print(f"   📄 Text Report: investigation_report_{result.investigation_id}_*.txt")
    console.print(f"   📊 Visualization: investigation_{result.investigation_id}_*.html")
    console.print(f"   📦 Evidence Package: evidence_package_{result.investigation_id}.json")

@cli.command()
@click.option('--address', '-a', required=True, help='Address to validate')
@click.option('--network', '-n', default='bitcoin', help='Blockchain network (bitcoin, ethereum, etc.)')
def validate(address, network):
    """Validate a cryptocurrency address."""
    from ..utils.validators import AddressValidator

    validator = AddressValidator()
    result = validator.validate_address(address, network)

    if result.is_valid:
        console.print(f"✅ Valid {network} address", style="green")
        console.print(f"   Type: {result.address_type}")
        console.print(f"   Confidence: {result.confidence:.2f}")
    else:
        console.print(f"❌ Invalid address: {result.error_message}", style="red")

@cli.command()
def version():
    """Show version information."""
    console.print(f"CryptoForensics v{__version__}", style="bold cyan")
    console.print("Professional Cryptocurrency Investigation Tool")
    console.print("Developed by the Cryptocurrency Investigation Team")

# Add victim management commands
cli.add_command(victim_cli)

def main():
    """Main entry point for the CLI."""
    try:
        cli()
    except KeyboardInterrupt:
        console.print("\n\n⚠️  Investigation interrupted by user.", style="yellow")
        sys.exit(1)
    except Exception as e:
        console.print(f"\n❌ An unexpected error occurred: {e}", style="bold red")
        sys.exit(1)

if __name__ == "__main__":
    main()
