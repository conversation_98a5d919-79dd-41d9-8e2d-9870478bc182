"""
Enhanced evidence collection and chain of custody for victim-centric investigations.

Provides specialized evidence handling, digital signatures, timestamps, and
legal admissibility features specifically designed for victim cases.
"""

import asyncio
import logging
import json
import uuid
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from pathlib import Path
from enum import Enum

from ..core.config import InvestigationConfig
from ..exceptions import EvidenceError
from ..utils.performance import performance_monitor
from ..utils.crypto import CryptoUtils
from ..utils.time_utils import TimeUtils
from .collector import EvidenceCollector, EvidenceItem
from .audit_trail import AuditTrail, AuditLogEntry

logger = logging.getLogger(__name__)

class VictimEvidenceType(Enum):
    """Types of victim-specific evidence."""
    VICTIM_STATEMENT = "victim_statement"
    INCIDENT_REPORT = "incident_report"
    COMMUNICATION_LOG = "communication_log"
    CONSENT_FORM = "consent_form"
    IDENTITY_VERIFICATION = "identity_verification"
    LOSS_DOCUMENTATION = "loss_documentation"
    RECOVERY_DOCUMENTATION = "recovery_documentation"
    VICTIM_UPDATE = "victim_update"

class LegalAdmissibilityLevel(Enum):
    """Legal admissibility levels for evidence."""
    COURT_READY = "court_ready"
    LEGAL_REVIEW_REQUIRED = "legal_review_required"
    SUPPORTING_EVIDENCE = "supporting_evidence"
    INTERNAL_USE_ONLY = "internal_use_only"

@dataclass
class VictimEvidenceItem(EvidenceItem):
    """Enhanced evidence item for victim cases."""
    victim_id: str = ""
    case_id: str = ""
    evidence_category: VictimEvidenceType = VictimEvidenceType.VICTIM_STATEMENT
    legal_admissibility: LegalAdmissibilityLevel = LegalAdmissibilityLevel.SUPPORTING_EVIDENCE
    victim_consent: bool = False
    gdpr_compliant: bool = True
    retention_period_days: int = 2555  # 7 years default
    digital_signature: Optional[str] = None
    witness_signatures: List[str] = field(default_factory=list)
    legal_hold: bool = False

    def __post_init__(self):
        """Post-initialization for victim evidence."""
        super().__post_init__()
        if not self.victim_id:
            logger.warning(f"Victim ID not set for evidence item: {self.evidence_id}")

@dataclass
class VictimChainOfCustodyEntry:
    """Enhanced chain of custody entry for victim cases."""
    entry_id: str
    evidence_id: str
    victim_id: str
    case_id: str
    timestamp: str
    action: str
    user_id: str
    description: str
    location: str
    hash_before: str
    hash_after: str
    digital_signature: Optional[str] = None
    witness_id: Optional[str] = None
    legal_authorization: Optional[str] = None
    gdpr_basis: str = "legitimate_interest"

    def __post_init__(self):
        """Post-initialization processing."""
        if not self.entry_id:
            self.entry_id = str(uuid.uuid4())

class VictimEvidenceCollector:
    """
    Enhanced evidence collector for victim-centric investigations.

    Provides:
    - Victim-specific evidence handling
    - Enhanced chain of custody with digital signatures
    - Legal admissibility features
    - GDPR compliance and data protection
    - Automated retention policy management
    """

    def __init__(self, investigation_id: str, config: InvestigationConfig,
                 victim_id: Optional[str] = None, case_id: Optional[str] = None):
        """
        Initialize victim evidence collector.

        Args:
            investigation_id: Investigation identifier
            config: Investigation configuration
            victim_id: Victim identifier
            case_id: Case identifier
        """
        self.investigation_id = investigation_id
        self.config = config
        self.victim_id = victim_id
        self.case_id = case_id

        # Initialize base collector
        self.base_collector = EvidenceCollector(investigation_id, config)

        # Victim-specific evidence storage
        self.victim_evidence_items: List[VictimEvidenceItem] = []
        self.victim_custody_chain: Dict[str, List[VictimChainOfCustodyEntry]] = {}

        # Create victim evidence directory
        if victim_id:
            self.victim_evidence_dir = Path(config.output_directory) / "investigations" / victim_id / "evidence"
            self.victim_evidence_dir.mkdir(parents=True, exist_ok=True)

        logger.info(f"Victim evidence collector initialized for victim: {victim_id}")

    @performance_monitor("victim_evidence_creation")
    async def create_victim_evidence_async(self,
                                         evidence_type: VictimEvidenceType,
                                         description: str,
                                         data: Dict[str, Any],
                                         victim_consent: bool = True,
                                         legal_admissibility: LegalAdmissibilityLevel = LegalAdmissibilityLevel.SUPPORTING_EVIDENCE,
                                         tags: Optional[List[str]] = None,
                                         witness_id: Optional[str] = None) -> VictimEvidenceItem:
        """
        Create victim-specific evidence item with enhanced chain of custody.

        Args:
            evidence_type: Type of victim evidence
            description: Evidence description
            data: Evidence data
            victim_consent: Whether victim has consented to data processing
            legal_admissibility: Legal admissibility level
            tags: Optional tags
            witness_id: Optional witness identifier

        Returns:
            Created VictimEvidenceItem
        """
        try:
            # Create enhanced evidence item
            evidence_item = VictimEvidenceItem(
                evidence_id=str(uuid.uuid4()),
                investigation_id=self.investigation_id,
                evidence_type=evidence_type.value,
                description=description,
                data=data,
                timestamp=datetime.now().isoformat(),
                hash_value="",  # Will be calculated
                tags=tags or [],
                classification="confidential",
                chain_of_custody=[],
                victim_id=self.victim_id or "",
                case_id=self.case_id or "",
                evidence_category=evidence_type,
                legal_admissibility=legal_admissibility,
                victim_consent=victim_consent,
                gdpr_compliant=True
            )

            # Calculate hash
            evidence_item.hash_value = evidence_item._calculate_hash()

            # Create initial chain of custody entry
            initial_custody = VictimChainOfCustodyEntry(
                entry_id=str(uuid.uuid4()),
                evidence_id=evidence_item.evidence_id,
                victim_id=self.victim_id or "",
                case_id=self.case_id or "",
                timestamp=datetime.now().isoformat(),
                action="evidence_created",
                user_id="system",
                description=f"Created {evidence_type.value} evidence",
                location=str(self.victim_evidence_dir) if hasattr(self, 'victim_evidence_dir') else "system",
                hash_before="",
                hash_after=evidence_item.hash_value,
                witness_id=witness_id,
                gdpr_basis="consent" if victim_consent else "legitimate_interest"
            )

            # Add digital signature if available
            if self.config.evidence_integrity_checks:
                initial_custody.digital_signature = self._create_digital_signature(initial_custody)

            # Add to custody chain
            evidence_item.chain_of_custody.append(asdict(initial_custody))

            # Store evidence
            self.victim_evidence_items.append(evidence_item)
            if evidence_item.evidence_id not in self.victim_custody_chain:
                self.victim_custody_chain[evidence_item.evidence_id] = []
            self.victim_custody_chain[evidence_item.evidence_id].append(initial_custody)

            # Save to disk
            await self._save_victim_evidence_async(evidence_item)

            logger.info(f"Created victim evidence: {evidence_item.evidence_id}")
            return evidence_item

        except Exception as e:
            logger.error(f"Error creating victim evidence: {e}")
            raise EvidenceError(f"Failed to create victim evidence: {e}")

    async def add_victim_custody_entry_async(self,
                                           evidence_id: str,
                                           action: str,
                                           user_id: str,
                                           description: str,
                                           witness_id: Optional[str] = None,
                                           legal_authorization: Optional[str] = None) -> VictimChainOfCustodyEntry:
        """
        Add enhanced chain of custody entry for victim evidence.

        Args:
            evidence_id: Evidence identifier
            action: Action performed
            user_id: User performing action
            description: Action description
            witness_id: Optional witness identifier
            legal_authorization: Optional legal authorization reference

        Returns:
            Created custody entry
        """
        try:
            # Find evidence item
            evidence_item = None
            for item in self.victim_evidence_items:
                if item.evidence_id == evidence_id:
                    evidence_item = item
                    break

            if not evidence_item:
                raise EvidenceError(f"Evidence item not found: {evidence_id}")

            # Get previous hash
            previous_entries = self.victim_custody_chain.get(evidence_id, [])
            previous_hash = previous_entries[-1].hash_after if previous_entries else ""

            # Create custody entry
            custody_entry = VictimChainOfCustodyEntry(
                entry_id=str(uuid.uuid4()),
                evidence_id=evidence_id,
                victim_id=self.victim_id or "",
                case_id=self.case_id or "",
                timestamp=datetime.now().isoformat(),
                action=action,
                user_id=user_id,
                description=description,
                location=str(self.victim_evidence_dir) if hasattr(self, 'victim_evidence_dir') else "system",
                hash_before=previous_hash,
                hash_after=evidence_item._calculate_hash(),
                witness_id=witness_id,
                legal_authorization=legal_authorization,
                gdpr_basis="legitimate_interest"
            )

            # Add digital signature
            if self.config.evidence_integrity_checks:
                custody_entry.digital_signature = self._create_digital_signature(custody_entry)

            # Add to chains
            evidence_item.chain_of_custody.append(asdict(custody_entry))
            if evidence_id not in self.victim_custody_chain:
                self.victim_custody_chain[evidence_id] = []
            self.victim_custody_chain[evidence_id].append(custody_entry)

            # Update evidence item
            evidence_item.updated_at = datetime.now().isoformat()

            # Save updates
            await self._save_victim_evidence_async(evidence_item)

            logger.info(f"Added custody entry: {custody_entry.entry_id}")
            return custody_entry

        except Exception as e:
            logger.error(f"Error adding custody entry: {e}")
            raise EvidenceError(f"Failed to add custody entry: {e}")

    def _create_digital_signature(self, custody_entry: VictimChainOfCustodyEntry) -> str:
        """Create digital signature for custody entry."""
        try:
            # Create signature data
            signature_data = {
                "entry_id": custody_entry.entry_id,
                "evidence_id": custody_entry.evidence_id,
                "timestamp": custody_entry.timestamp,
                "action": custody_entry.action,
                "user_id": custody_entry.user_id,
                "hash_after": custody_entry.hash_after
            }

            # Create signature (simplified - in production would use proper PKI)
            signature_string = json.dumps(signature_data, sort_keys=True)
            signature_hash = CryptoUtils.calculate_sha256(signature_string)

            return f"SIG_{signature_hash[:16]}"

        except Exception as e:
            logger.error(f"Error creating digital signature: {e}")
            return "SIGNATURE_ERROR"

    async def _save_victim_evidence_async(self, evidence_item: VictimEvidenceItem) -> None:
        """Save victim evidence item to disk."""
        try:
            if not hasattr(self, 'victim_evidence_dir'):
                return

            filename = f"victim_evidence_{evidence_item.evidence_id}.json"
            filepath = self.victim_evidence_dir / filename

            # Prepare evidence data
            evidence_data = asdict(evidence_item)

            # Add metadata
            evidence_data["saved_at"] = datetime.now().isoformat()
            evidence_data["file_version"] = "3.0"
            evidence_data["compliance_status"] = {
                "gdpr_compliant": evidence_item.gdpr_compliant,
                "victim_consent": evidence_item.victim_consent,
                "legal_admissibility": evidence_item.legal_admissibility.value,
                "retention_expires": (datetime.now() +
                                    timedelta(days=evidence_item.retention_period_days)).isoformat()
            }

            # Save with encryption if enabled
            if self.config.evidence_integrity_checks:
                # In production, this would use proper encryption
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(evidence_data, f, indent=2, default=str)
            else:
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(evidence_data, f, indent=2, default=str)

            logger.debug(f"Saved victim evidence: {filepath}")

        except Exception as e:
            logger.error(f"Error saving victim evidence: {e}")
            raise EvidenceError(f"Failed to save victim evidence: {e}")

    async def verify_victim_evidence_integrity_async(self, evidence_id: str) -> Dict[str, Any]:
        """
        Verify integrity of victim evidence and chain of custody.

        Args:
            evidence_id: Evidence identifier to verify

        Returns:
            Verification results
        """
        try:
            # Find evidence item
            evidence_item = None
            for item in self.victim_evidence_items:
                if item.evidence_id == evidence_id:
                    evidence_item = item
                    break

            if not evidence_item:
                return {
                    "evidence_id": evidence_id,
                    "integrity_status": "FAILED",
                    "error": "Evidence item not found"
                }

            # Verify evidence hash
            current_hash = evidence_item._calculate_hash()
            hash_valid = current_hash == evidence_item.hash_value

            # Verify chain of custody
            custody_chain = self.victim_custody_chain.get(evidence_id, [])
            chain_valid = True
            chain_issues = []

            for i, entry in enumerate(custody_chain):
                # Verify digital signature if present
                if entry.digital_signature:
                    expected_signature = self._create_digital_signature(entry)
                    if entry.digital_signature != expected_signature:
                        chain_valid = False
                        chain_issues.append(f"Invalid signature in entry {i}")

                # Verify hash chain
                if i > 0:
                    previous_entry = custody_chain[i-1]
                    if entry.hash_before != previous_entry.hash_after:
                        chain_valid = False
                        chain_issues.append(f"Hash chain broken at entry {i}")

            # Check legal compliance
            compliance_status = self._check_legal_compliance(evidence_item)

            return {
                "evidence_id": evidence_id,
                "integrity_status": "VERIFIED" if hash_valid and chain_valid else "COMPROMISED",
                "hash_verification": {
                    "status": "VALID" if hash_valid else "INVALID",
                    "expected_hash": evidence_item.hash_value,
                    "current_hash": current_hash
                },
                "chain_of_custody": {
                    "status": "VALID" if chain_valid else "INVALID",
                    "total_entries": len(custody_chain),
                    "issues": chain_issues
                },
                "legal_compliance": compliance_status,
                "verification_timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error verifying evidence integrity: {e}")
            return {
                "evidence_id": evidence_id,
                "integrity_status": "ERROR",
                "error": str(e)
            }

    def _check_legal_compliance(self, evidence_item: VictimEvidenceItem) -> Dict[str, Any]:
        """Check legal compliance status of evidence item."""
        compliance_issues = []

        # Check victim consent
        if not evidence_item.victim_consent and evidence_item.evidence_category in [
            VictimEvidenceType.VICTIM_STATEMENT,
            VictimEvidenceType.COMMUNICATION_LOG,
            VictimEvidenceType.IDENTITY_VERIFICATION
        ]:
            compliance_issues.append("Victim consent required for this evidence type")

        # Check GDPR compliance
        if not evidence_item.gdpr_compliant:
            compliance_issues.append("Evidence not marked as GDPR compliant")

        # Check retention period
        creation_date = TimeUtils.parse_timestamp(evidence_item.timestamp)
        if creation_date:
            retention_expires = creation_date + timedelta(days=evidence_item.retention_period_days)
            if datetime.now() > retention_expires and not evidence_item.legal_hold:
                compliance_issues.append("Evidence retention period expired")

        # Check digital signatures for court-ready evidence
        if evidence_item.legal_admissibility == LegalAdmissibilityLevel.COURT_READY:
            custody_entries = self.victim_custody_chain.get(evidence_item.evidence_id, [])
            if not all(entry.digital_signature for entry in custody_entries):
                compliance_issues.append("Digital signatures required for court-ready evidence")

        return {
            "compliant": len(compliance_issues) == 0,
            "issues": compliance_issues,
            "legal_admissibility": evidence_item.legal_admissibility.value,
            "gdpr_status": "compliant" if evidence_item.gdpr_compliant else "non_compliant",
            "consent_status": "obtained" if evidence_item.victim_consent else "not_required"
        }

    async def generate_victim_evidence_report_async(self) -> Dict[str, Any]:
        """
        Generate comprehensive evidence report for victim case.

        Returns:
            Evidence report
        """
        try:
            total_evidence = len(self.victim_evidence_items)
            evidence_by_type = {}
            evidence_by_admissibility = {}
            compliance_summary = {
                "compliant": 0,
                "non_compliant": 0,
                "issues": []
            }

            # Analyze evidence items
            for item in self.victim_evidence_items:
                # Count by type
                evidence_type = item.evidence_category.value
                evidence_by_type[evidence_type] = evidence_by_type.get(evidence_type, 0) + 1

                # Count by admissibility
                admissibility = item.legal_admissibility.value
                evidence_by_admissibility[admissibility] = evidence_by_admissibility.get(admissibility, 0) + 1

                # Check compliance
                compliance = self._check_legal_compliance(item)
                if compliance["compliant"]:
                    compliance_summary["compliant"] += 1
                else:
                    compliance_summary["non_compliant"] += 1
                    compliance_summary["issues"].extend(compliance["issues"])

            # Verify all evidence integrity
            integrity_results = []
            for item in self.victim_evidence_items:
                verification = await self.verify_victim_evidence_integrity_async(item.evidence_id)
                integrity_results.append(verification)

            verified_count = sum(1 for r in integrity_results if r["integrity_status"] == "VERIFIED")

            return {
                "report_metadata": {
                    "victim_id": self.victim_id,
                    "case_id": self.case_id,
                    "investigation_id": self.investigation_id,
                    "generated_at": datetime.now().isoformat(),
                    "report_type": "victim_evidence_summary"
                },
                "evidence_summary": {
                    "total_evidence_items": total_evidence,
                    "evidence_by_type": evidence_by_type,
                    "evidence_by_admissibility": evidence_by_admissibility,
                    "integrity_verified": verified_count,
                    "integrity_compromised": total_evidence - verified_count
                },
                "compliance_summary": compliance_summary,
                "chain_of_custody_summary": {
                    "total_custody_entries": sum(len(chain) for chain in self.victim_custody_chain.values()),
                    "evidence_with_custody": len(self.victim_custody_chain),
                    "digital_signatures_present": sum(
                        1 for chain in self.victim_custody_chain.values()
                        for entry in chain if entry.digital_signature
                    )
                },
                "integrity_verification": integrity_results,
                "recommendations": self._generate_evidence_recommendations(compliance_summary, integrity_results)
            }

        except Exception as e:
            logger.error(f"Error generating evidence report: {e}")
            return {
                "error": f"Failed to generate evidence report: {e}",
                "generated_at": datetime.now().isoformat()
            }

    def _generate_evidence_recommendations(self, compliance_summary: Dict[str, Any],
                                         integrity_results: List[Dict[str, Any]]) -> List[str]:
        """Generate recommendations based on evidence analysis."""
        recommendations = []

        # Compliance recommendations
        if compliance_summary["non_compliant"] > 0:
            recommendations.append("Review and address compliance issues before legal proceedings")

        # Integrity recommendations
        compromised_count = sum(1 for r in integrity_results if r["integrity_status"] == "COMPROMISED")
        if compromised_count > 0:
            recommendations.append(f"Investigate {compromised_count} evidence items with integrity issues")

        # Digital signature recommendations
        unsigned_count = sum(1 for r in integrity_results
                           if r.get("chain_of_custody", {}).get("issues") and
                           any("signature" in issue for issue in r["chain_of_custody"]["issues"]))
        if unsigned_count > 0:
            recommendations.append("Add digital signatures to enhance legal admissibility")

        # General recommendations
        if not recommendations:
            recommendations.append("Evidence collection meets professional standards")

        return recommendations

    async def export_victim_evidence_package_async(self, export_path: Optional[str] = None) -> str:
        """
        Export complete victim evidence package for legal proceedings.

        Args:
            export_path: Optional export path

        Returns:
            Path to exported package
        """
        try:
            if not export_path:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                export_filename = f"victim_evidence_package_{self.victim_id}_{timestamp}.json"
                export_path = str(Path(self.config.output_directory) / "exports" / export_filename)

            # Create export directory
            Path(export_path).parent.mkdir(parents=True, exist_ok=True)

            # Generate comprehensive report
            evidence_report = await self.generate_victim_evidence_report_async()

            # Create export package
            export_package = {
                "package_metadata": {
                    "package_type": "victim_evidence_export",
                    "victim_id": self.victim_id,
                    "case_id": self.case_id,
                    "investigation_id": self.investigation_id,
                    "exported_at": datetime.now().isoformat(),
                    "export_version": "3.0",
                    "legal_certification": "This package contains legally admissible evidence"
                },
                "evidence_items": [asdict(item) for item in self.victim_evidence_items],
                "chain_of_custody": {
                    evidence_id: [asdict(entry) for entry in chain]
                    for evidence_id, chain in self.victim_custody_chain.items()
                },
                "evidence_report": evidence_report,
                "integrity_verification": {
                    "verified_at": datetime.now().isoformat(),
                    "verification_method": "cryptographic_hash_chain",
                    "digital_signatures": "present" if any(
                        entry.digital_signature for chain in self.victim_custody_chain.values()
                        for entry in chain
                    ) else "not_present"
                }
            }

            # Save export package
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_package, f, indent=2, default=str)

            logger.info(f"Exported victim evidence package: {export_path}")
            return export_path

        except Exception as e:
            logger.error(f"Error exporting evidence package: {e}")
            raise EvidenceError(f"Failed to export evidence package: {e}")
