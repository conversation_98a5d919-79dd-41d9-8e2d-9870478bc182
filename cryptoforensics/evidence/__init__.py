"""
Evidence collection and management module for CryptoForensics

Provides professional-grade evidence collection, chain of custody management,
audit trail functionality, blockchain timestamping, and automated compliance
meeting legal standards.
"""

from .collector import EvidenceCollector, EvidenceItem
from .audit_trail import AuditTrail, AuditLogEntry
from .blockchain_timestamping import BlockchainTimestamper, BlockchainTimestamp, TimestampVerificationResult
from .compliance import ComplianceManager, ComplianceReport, ComplianceViolation, ComplianceStandard

__all__ = [
    # Core evidence functionality
    "EvidenceCollector",
    "EvidenceItem",
    "AuditTrail",
    "AuditLogEntry",

    # Advanced evidence features
    "BlockchainTimestamper",
    "BlockchainTimestamp",
    "TimestampVerificationResult",
    "ComplianceManager",
    "ComplianceReport",
    "ComplianceViolation",
    "ComplianceStandard"
]
