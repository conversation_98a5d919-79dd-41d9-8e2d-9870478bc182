"""
Automated compliance and legal standards for CryptoForensics

Provides automated compliance checking, legal standard adherence,
and regulatory requirement validation for cryptocurrency investigations.
"""

import logging
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

from ..core.config import InvestigationConfig
from ..exceptions import EvidenceError
from ..utils.performance import performance_monitor
from ..utils.time_utils import TimeUtils

logger = logging.getLogger(__name__)

class ComplianceStandard(Enum):
    """Supported compliance standards."""
    GDPR = "gdpr"
    CCPA = "ccpa"
    SOX = "sox"
    FINRA = "finra"
    BSA = "bsa"  # Bank Secrecy Act
    AML = "aml"  # Anti-Money Laundering
    KYC = "kyc"  # Know Your Customer
    ISO27001 = "iso27001"
    NIST = "nist"
    FIPS = "fips"

class DataClassification(Enum):
    """Data classification levels."""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"
    TOP_SECRET = "top_secret"

class RetentionPolicy(Enum):
    """Data retention policies."""
    SHORT_TERM = "short_term"  # 30 days
    MEDIUM_TERM = "medium_term"  # 1 year
    LONG_TERM = "long_term"  # 7 years
    PERMANENT = "permanent"
    LEGAL_HOLD = "legal_hold"

@dataclass
class ComplianceRule:
    """Compliance rule definition."""
    rule_id: str
    standard: ComplianceStandard
    title: str
    description: str
    requirements: List[str]
    data_types: List[str]
    retention_period: RetentionPolicy
    access_controls: Dict[str, Any]
    audit_requirements: Dict[str, Any]

@dataclass
class ComplianceViolation:
    """Compliance violation record."""
    violation_id: str
    rule_id: str
    severity: str
    description: str
    evidence_id: str
    detected_at: str
    remediation_required: bool
    remediation_steps: List[str]

@dataclass
class ComplianceReport:
    """Compliance assessment report."""
    assessment_id: str
    investigation_id: str
    standards_checked: List[ComplianceStandard]
    compliance_score: float
    violations: List[ComplianceViolation]
    recommendations: List[str]
    next_review_date: str
    generated_at: str

class ComplianceManager:
    """
    Automated compliance management system.
    
    Provides comprehensive compliance checking, violation detection,
    and automated remediation for cryptocurrency investigations.
    """
    
    def __init__(self, config: InvestigationConfig):
        """
        Initialize compliance manager.
        
        Args:
            config: Investigation configuration
        """
        self.config = config
        self.compliance_rules = self._load_compliance_rules()
        self.jurisdiction_requirements = self._load_jurisdiction_requirements()
        self.violation_history = []
        
        logger.info("Compliance manager initialized")
    
    def _load_compliance_rules(self) -> Dict[str, ComplianceRule]:
        """Load compliance rules for different standards."""
        rules = {}
        
        # GDPR Rules
        rules["gdpr_data_minimization"] = ComplianceRule(
            rule_id="gdpr_data_minimization",
            standard=ComplianceStandard.GDPR,
            title="Data Minimization",
            description="Collect only necessary data for investigation purposes",
            requirements=[
                "Document justification for data collection",
                "Limit data collection to investigation scope",
                "Regular review of collected data necessity"
            ],
            data_types=["personal_data", "financial_data", "transaction_data"],
            retention_period=RetentionPolicy.MEDIUM_TERM,
            access_controls={
                "minimum_clearance": "confidential",
                "need_to_know": True,
                "audit_access": True
            },
            audit_requirements={
                "log_all_access": True,
                "retention_audit": True,
                "deletion_audit": True
            }
        )
        
        rules["gdpr_consent"] = ComplianceRule(
            rule_id="gdpr_consent",
            standard=ComplianceStandard.GDPR,
            title="Lawful Basis for Processing",
            description="Ensure lawful basis exists for processing personal data",
            requirements=[
                "Document legal basis for investigation",
                "Maintain consent records where applicable",
                "Respect data subject rights"
            ],
            data_types=["personal_data", "biometric_data", "location_data"],
            retention_period=RetentionPolicy.LONG_TERM,
            access_controls={
                "minimum_clearance": "restricted",
                "legal_approval": True
            },
            audit_requirements={
                "legal_basis_audit": True,
                "consent_audit": True
            }
        )
        
        # AML/BSA Rules
        rules["aml_suspicious_activity"] = ComplianceRule(
            rule_id="aml_suspicious_activity",
            standard=ComplianceStandard.AML,
            title="Suspicious Activity Reporting",
            description="Report suspicious cryptocurrency activities",
            requirements=[
                "Monitor for suspicious patterns",
                "File SARs within required timeframes",
                "Maintain detailed investigation records"
            ],
            data_types=["transaction_data", "wallet_data", "exchange_data"],
            retention_period=RetentionPolicy.LONG_TERM,
            access_controls={
                "minimum_clearance": "confidential",
                "aml_officer_approval": True
            },
            audit_requirements={
                "sar_filing_audit": True,
                "investigation_audit": True
            }
        )
        
        # FINRA Rules
        rules["finra_recordkeeping"] = ComplianceRule(
            rule_id="finra_recordkeeping",
            standard=ComplianceStandard.FINRA,
            title="Recordkeeping Requirements",
            description="Maintain comprehensive investigation records",
            requirements=[
                "Preserve all investigation materials",
                "Maintain chain of custody",
                "Ensure record accessibility"
            ],
            data_types=["all_investigation_data"],
            retention_period=RetentionPolicy.LONG_TERM,
            access_controls={
                "minimum_clearance": "internal",
                "regulatory_access": True
            },
            audit_requirements={
                "record_integrity_audit": True,
                "access_audit": True
            }
        )
        
        # ISO 27001 Rules
        rules["iso27001_access_control"] = ComplianceRule(
            rule_id="iso27001_access_control",
            standard=ComplianceStandard.ISO27001,
            title="Access Control Management",
            description="Implement proper access controls for investigation data",
            requirements=[
                "Role-based access control",
                "Regular access reviews",
                "Privileged access management"
            ],
            data_types=["all_data"],
            retention_period=RetentionPolicy.PERMANENT,
            access_controls={
                "rbac_required": True,
                "mfa_required": True,
                "access_review_frequency": "quarterly"
            },
            audit_requirements={
                "access_control_audit": True,
                "privilege_audit": True
            }
        )
        
        return rules
    
    def _load_jurisdiction_requirements(self) -> Dict[str, Dict[str, Any]]:
        """Load jurisdiction-specific requirements."""
        return {
            "US": {
                "required_standards": [ComplianceStandard.BSA, ComplianceStandard.AML, ComplianceStandard.FINRA],
                "data_localization": False,
                "cross_border_restrictions": ["iran", "north_korea", "syria"],
                "reporting_requirements": {
                    "sar_threshold": 10000,  # USD
                    "ctr_threshold": 10000,  # USD
                    "timeframe": 30  # days
                }
            },
            "EU": {
                "required_standards": [ComplianceStandard.GDPR, ComplianceStandard.AML],
                "data_localization": True,
                "cross_border_restrictions": [],
                "reporting_requirements": {
                    "suspicious_activity_threshold": 15000,  # EUR
                    "timeframe": 15  # days
                }
            },
            "UK": {
                "required_standards": [ComplianceStandard.AML, ComplianceStandard.GDPR],
                "data_localization": False,
                "cross_border_restrictions": [],
                "reporting_requirements": {
                    "sar_threshold": 10000,  # GBP
                    "timeframe": 7  # days
                }
            }
        }
    
    @performance_monitor("compliance_assessment")
    async def assess_compliance_async(self, investigation_data: Dict[str, Any], 
                                    jurisdiction: str = "US") -> ComplianceReport:
        """
        Perform comprehensive compliance assessment.
        
        Args:
            investigation_data: Investigation data to assess
            jurisdiction: Legal jurisdiction for compliance
            
        Returns:
            ComplianceReport with assessment results
        """
        try:
            logger.info(f"Starting compliance assessment for jurisdiction: {jurisdiction}")
            
            # Get jurisdiction requirements
            jurisdiction_req = self.jurisdiction_requirements.get(jurisdiction, {})
            required_standards = jurisdiction_req.get("required_standards", [])
            
            # Assess compliance for each required standard
            violations = []
            compliance_scores = []
            
            for standard in required_standards:
                standard_violations, standard_score = await self._assess_standard_compliance(
                    investigation_data, standard, jurisdiction_req
                )
                violations.extend(standard_violations)
                compliance_scores.append(standard_score)
            
            # Calculate overall compliance score
            overall_score = sum(compliance_scores) / len(compliance_scores) if compliance_scores else 0.0
            
            # Generate recommendations
            recommendations = self._generate_compliance_recommendations(violations, jurisdiction)
            
            # Create compliance report
            report = ComplianceReport(
                assessment_id=f"comp_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                investigation_id=investigation_data.get("investigation_id", "unknown"),
                standards_checked=required_standards,
                compliance_score=overall_score,
                violations=violations,
                recommendations=recommendations,
                next_review_date=(datetime.now() + timedelta(days=90)).isoformat(),
                generated_at=datetime.now().isoformat()
            )
            
            logger.info(f"Compliance assessment completed: {overall_score:.2f} score, {len(violations)} violations")
            return report
            
        except Exception as e:
            logger.error(f"Compliance assessment failed: {e}")
            raise EvidenceError(f"Compliance assessment failed: {e}")
    
    async def _assess_standard_compliance(self, investigation_data: Dict[str, Any],
                                        standard: ComplianceStandard,
                                        jurisdiction_req: Dict[str, Any]) -> tuple[List[ComplianceViolation], float]:
        """Assess compliance for a specific standard."""
        violations = []
        compliance_score = 1.0
        
        # Get rules for this standard
        standard_rules = [rule for rule in self.compliance_rules.values() 
                         if rule.standard == standard]
        
        for rule in standard_rules:
            rule_violations = await self._check_rule_compliance(investigation_data, rule, jurisdiction_req)
            violations.extend(rule_violations)
            
            # Reduce compliance score for each violation
            if rule_violations:
                severity_weights = {"critical": 0.3, "high": 0.2, "medium": 0.1, "low": 0.05}
                for violation in rule_violations:
                    weight = severity_weights.get(violation.severity, 0.1)
                    compliance_score -= weight
        
        return violations, max(0.0, compliance_score)
    
    async def _check_rule_compliance(self, investigation_data: Dict[str, Any],
                                   rule: ComplianceRule,
                                   jurisdiction_req: Dict[str, Any]) -> List[ComplianceViolation]:
        """Check compliance for a specific rule."""
        violations = []
        
        # Check data minimization (GDPR)
        if rule.rule_id == "gdpr_data_minimization":
            violations.extend(self._check_data_minimization(investigation_data, rule))
        
        # Check consent and legal basis (GDPR)
        elif rule.rule_id == "gdpr_consent":
            violations.extend(self._check_legal_basis(investigation_data, rule))
        
        # Check suspicious activity reporting (AML)
        elif rule.rule_id == "aml_suspicious_activity":
            violations.extend(self._check_suspicious_activity_reporting(investigation_data, rule, jurisdiction_req))
        
        # Check recordkeeping (FINRA)
        elif rule.rule_id == "finra_recordkeeping":
            violations.extend(self._check_recordkeeping(investigation_data, rule))
        
        # Check access control (ISO 27001)
        elif rule.rule_id == "iso27001_access_control":
            violations.extend(self._check_access_control(investigation_data, rule))
        
        return violations
    
    def _check_data_minimization(self, investigation_data: Dict[str, Any], 
                                rule: ComplianceRule) -> List[ComplianceViolation]:
        """Check GDPR data minimization compliance."""
        violations = []
        
        # Check if data collection is justified
        if not investigation_data.get("data_collection_justification"):
            violations.append(ComplianceViolation(
                violation_id=f"gdpr_min_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                rule_id=rule.rule_id,
                severity="high",
                description="Missing data collection justification",
                evidence_id=investigation_data.get("investigation_id", "unknown"),
                detected_at=datetime.now().isoformat(),
                remediation_required=True,
                remediation_steps=[
                    "Document justification for all collected data",
                    "Review data necessity for investigation scope",
                    "Remove unnecessary data elements"
                ]
            ))
        
        # Check for excessive data collection
        collected_data_types = investigation_data.get("data_types_collected", [])
        if len(collected_data_types) > 10:  # Arbitrary threshold
            violations.append(ComplianceViolation(
                violation_id=f"gdpr_excess_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                rule_id=rule.rule_id,
                severity="medium",
                description="Potentially excessive data collection detected",
                evidence_id=investigation_data.get("investigation_id", "unknown"),
                detected_at=datetime.now().isoformat(),
                remediation_required=True,
                remediation_steps=[
                    "Review necessity of all collected data types",
                    "Remove non-essential data",
                    "Update data collection procedures"
                ]
            ))
        
        return violations
    
    def _check_legal_basis(self, investigation_data: Dict[str, Any], 
                          rule: ComplianceRule) -> List[ComplianceViolation]:
        """Check GDPR legal basis compliance."""
        violations = []
        
        if not investigation_data.get("legal_basis"):
            violations.append(ComplianceViolation(
                violation_id=f"gdpr_basis_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                rule_id=rule.rule_id,
                severity="critical",
                description="Missing legal basis for data processing",
                evidence_id=investigation_data.get("investigation_id", "unknown"),
                detected_at=datetime.now().isoformat(),
                remediation_required=True,
                remediation_steps=[
                    "Document legal basis for investigation",
                    "Obtain necessary legal approvals",
                    "Update investigation authorization"
                ]
            ))
        
        return violations
    
    def _check_suspicious_activity_reporting(self, investigation_data: Dict[str, Any],
                                           rule: ComplianceRule,
                                           jurisdiction_req: Dict[str, Any]) -> List[ComplianceViolation]:
        """Check AML suspicious activity reporting compliance."""
        violations = []
        
        # Check if suspicious activity was properly reported
        suspicious_activities = investigation_data.get("suspicious_activities", [])
        reporting_req = jurisdiction_req.get("reporting_requirements", {})
        threshold = reporting_req.get("sar_threshold", 10000)
        
        for activity in suspicious_activities:
            amount = activity.get("amount", 0)
            if amount >= threshold and not activity.get("sar_filed"):
                violations.append(ComplianceViolation(
                    violation_id=f"aml_sar_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    rule_id=rule.rule_id,
                    severity="critical",
                    description=f"Suspicious activity above threshold not reported: ${amount}",
                    evidence_id=activity.get("activity_id", "unknown"),
                    detected_at=datetime.now().isoformat(),
                    remediation_required=True,
                    remediation_steps=[
                        "File SAR immediately",
                        "Document suspicious activity details",
                        "Review reporting procedures"
                    ]
                ))
        
        return violations
    
    def _check_recordkeeping(self, investigation_data: Dict[str, Any], 
                           rule: ComplianceRule) -> List[ComplianceViolation]:
        """Check FINRA recordkeeping compliance."""
        violations = []
        
        # Check if chain of custody is maintained
        if not investigation_data.get("chain_of_custody"):
            violations.append(ComplianceViolation(
                violation_id=f"finra_custody_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                rule_id=rule.rule_id,
                severity="high",
                description="Missing chain of custody documentation",
                evidence_id=investigation_data.get("investigation_id", "unknown"),
                detected_at=datetime.now().isoformat(),
                remediation_required=True,
                remediation_steps=[
                    "Establish chain of custody procedures",
                    "Document all evidence handling",
                    "Train staff on custody requirements"
                ]
            ))
        
        return violations
    
    def _check_access_control(self, investigation_data: Dict[str, Any], 
                            rule: ComplianceRule) -> List[ComplianceViolation]:
        """Check ISO 27001 access control compliance."""
        violations = []
        
        # Check if proper access controls are in place
        access_controls = investigation_data.get("access_controls", {})
        if not access_controls.get("rbac_enabled"):
            violations.append(ComplianceViolation(
                violation_id=f"iso_rbac_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                rule_id=rule.rule_id,
                severity="high",
                description="Role-based access control not implemented",
                evidence_id=investigation_data.get("investigation_id", "unknown"),
                detected_at=datetime.now().isoformat(),
                remediation_required=True,
                remediation_steps=[
                    "Implement role-based access control",
                    "Define user roles and permissions",
                    "Regular access reviews"
                ]
            ))
        
        return violations
    
    def _generate_compliance_recommendations(self, violations: List[ComplianceViolation], 
                                           jurisdiction: str) -> List[str]:
        """Generate compliance recommendations."""
        recommendations = []
        
        if violations:
            recommendations.append("Address all identified compliance violations immediately")
            
            # Group by severity
            critical_violations = [v for v in violations if v.severity == "critical"]
            high_violations = [v for v in violations if v.severity == "high"]
            
            if critical_violations:
                recommendations.append("Critical violations require immediate attention and legal review")
            
            if high_violations:
                recommendations.append("High-severity violations should be resolved within 24 hours")
        
        # Jurisdiction-specific recommendations
        if jurisdiction == "EU":
            recommendations.append("Ensure GDPR compliance for all personal data processing")
            recommendations.append("Consider data localization requirements")
        elif jurisdiction == "US":
            recommendations.append("Maintain BSA/AML compliance for all financial investigations")
            recommendations.append("File required reports within regulatory timeframes")
        
        recommendations.append("Implement regular compliance monitoring and auditing")
        recommendations.append("Provide compliance training for all investigation staff")
        
        return recommendations
    
    def get_retention_policy(self, data_type: str, classification: DataClassification) -> RetentionPolicy:
        """Get appropriate retention policy for data type and classification."""
        # High-level data requires longer retention
        if classification in [DataClassification.RESTRICTED, DataClassification.TOP_SECRET]:
            return RetentionPolicy.LONG_TERM
        
        # Financial data typically requires 7-year retention
        if data_type in ["financial_data", "transaction_data", "aml_data"]:
            return RetentionPolicy.LONG_TERM
        
        # Personal data under GDPR
        if data_type == "personal_data":
            return RetentionPolicy.MEDIUM_TERM
        
        # Default retention
        return RetentionPolicy.SHORT_TERM
    
    def check_data_retention_compliance(self, evidence_items: List[Dict[str, Any]]) -> List[ComplianceViolation]:
        """Check data retention compliance for evidence items."""
        violations = []
        
        for item in evidence_items:
            created_date = TimeUtils.parse_timestamp(item.get("created_at", ""))
            if not created_date:
                continue
            
            data_type = item.get("data_type", "unknown")
            classification = DataClassification(item.get("classification", "internal"))
            retention_policy = self.get_retention_policy(data_type, classification)
            
            # Check if data should be deleted
            age_days = (datetime.now() - created_date).days
            
            if retention_policy == RetentionPolicy.SHORT_TERM and age_days > 30:
                violations.append(ComplianceViolation(
                    violation_id=f"retention_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    rule_id="data_retention",
                    severity="medium",
                    description=f"Data exceeds retention period: {age_days} days",
                    evidence_id=item.get("evidence_id", "unknown"),
                    detected_at=datetime.now().isoformat(),
                    remediation_required=True,
                    remediation_steps=[
                        "Review data necessity",
                        "Delete if no longer required",
                        "Update retention policies"
                    ]
                ))
        
        return violations
