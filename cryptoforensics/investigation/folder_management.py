"""
Professional investigation folder management system for CryptoForensics v3.0

Provides automated folder hierarchy creation, backup procedures, data retention policies,
and professional organization for victim-centric investigations.
"""

import asyncio
import logging
import json
import shutil
import tarfile
import gzip
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
from enum import Enum

from ..core.config import InvestigationConfig
from ..exceptions import InvestigationError
from ..utils.performance import performance_monitor
from ..utils.time_utils import TimeUtils

logger = logging.getLogger(__name__)

class RetentionPolicy(Enum):
    """Data retention policy levels."""
    SHORT_TERM = "short_term"  # 1 year
    STANDARD = "standard"      # 7 years (legal compliance)
    LONG_TERM = "long_term"    # 10 years
    PERMANENT = "permanent"    # No deletion

class BackupLevel(Enum):
    """Backup levels for different data types."""
    CRITICAL = "critical"      # Daily backups, multiple locations
    IMPORTANT = "important"    # Weekly backups
    STANDARD = "standard"      # Monthly backups
    MINIMAL = "minimal"        # Quarterly backups

@dataclass
class FolderStructure:
    """Professional investigation folder structure definition."""
    base_path: Path
    victim_id: str
    case_date: datetime

    # Main folders
    case_folder: Path
    evidence_folder: Path
    reports_folder: Path
    analysis_folder: Path
    communication_folder: Path
    audit_folder: Path
    exports_folder: Path
    backups_folder: Path

    # Metadata
    created_at: datetime
    retention_policy: RetentionPolicy
    backup_level: BackupLevel

    def __post_init__(self):
        """Initialize folder paths."""
        case_date_str = self.case_date.strftime('%Y%m%d')
        self.case_folder = self.base_path / self.victim_id / case_date_str

        # Create subfolder paths
        self.evidence_folder = self.case_folder / "evidence"
        self.reports_folder = self.case_folder / "reports"
        self.analysis_folder = self.case_folder / "analysis"
        self.communication_folder = self.case_folder / "communication"
        self.audit_folder = self.case_folder / "audit"
        self.exports_folder = self.case_folder / "exports"
        self.backups_folder = self.case_folder / "backups"

class InvestigationFolderManager:
    """
    Professional investigation folder management system.

    Provides:
    - Automated folder hierarchy creation
    - Backup procedures and scheduling
    - Data retention policy enforcement
    - Professional organization standards
    - Compliance with legal requirements
    """

    def __init__(self, config: InvestigationConfig):
        """
        Initialize folder manager.

        Args:
            config: Investigation configuration
        """
        self.config = config
        self.base_investigations_dir = Path(config.output_directory) / "investigations"
        self.base_backups_dir = Path(config.output_directory) / "backups"

        # Create base directories
        self.base_investigations_dir.mkdir(parents=True, exist_ok=True)
        self.base_backups_dir.mkdir(parents=True, exist_ok=True)

        # Retention policies (in days)
        self.retention_periods = {
            RetentionPolicy.SHORT_TERM: 365,      # 1 year
            RetentionPolicy.STANDARD: 2555,       # 7 years
            RetentionPolicy.LONG_TERM: 3650,      # 10 years
            RetentionPolicy.PERMANENT: None       # Never delete
        }

        # Backup schedules (in days)
        self.backup_schedules = {
            BackupLevel.CRITICAL: 1,      # Daily
            BackupLevel.IMPORTANT: 7,     # Weekly
            BackupLevel.STANDARD: 30,     # Monthly
            BackupLevel.MINIMAL: 90       # Quarterly
        }

        logger.info("Investigation folder manager initialized")

    @performance_monitor("folder_creation")
    def create_investigation_folder_structure(self, victim_id: str, case_date: datetime,
                                            retention_policy: RetentionPolicy = RetentionPolicy.STANDARD,
                                            backup_level: BackupLevel = BackupLevel.IMPORTANT) -> FolderStructure:
        """
        Create professional investigation folder structure.

        Args:
            victim_id: Victim identifier
            case_date: Case creation date
            retention_policy: Data retention policy
            backup_level: Backup level

        Returns:
            FolderStructure object
        """
        try:
            # Create folder structure
            structure = FolderStructure(
                base_path=self.base_investigations_dir,
                victim_id=victim_id,
                case_date=case_date,
                case_folder=Path(),  # Will be set in __post_init__
                evidence_folder=Path(),
                reports_folder=Path(),
                analysis_folder=Path(),
                communication_folder=Path(),
                audit_folder=Path(),
                exports_folder=Path(),
                backups_folder=Path(),
                created_at=datetime.now(),
                retention_policy=retention_policy,
                backup_level=backup_level
            )

            # Create all folders
            folders_to_create = [
                structure.case_folder,
                structure.evidence_folder,
                structure.reports_folder,
                structure.analysis_folder,
                structure.communication_folder,
                structure.audit_folder,
                structure.exports_folder,
                structure.backups_folder
            ]

            for folder in folders_to_create:
                folder.mkdir(parents=True, exist_ok=True)

                # Create .gitkeep file to preserve empty folders
                gitkeep_file = folder / ".gitkeep"
                gitkeep_file.touch()

            # Create folder metadata
            self._create_folder_metadata(structure)

            # Create README files for each folder
            self._create_folder_documentation(structure)

            # Set up retention policy
            self._setup_retention_policy(structure)

            # Schedule initial backup
            self._schedule_backup(structure)

            logger.info(f"Created investigation folder structure: {structure.case_folder}")
            return structure

        except Exception as e:
            logger.error(f"Error creating folder structure: {e}")
            raise InvestigationError(f"Failed to create folder structure: {e}")

    def _create_folder_metadata(self, structure: FolderStructure) -> None:
        """Create metadata file for folder structure."""
        metadata = {
            "folder_structure_version": "3.0",
            "victim_id": structure.victim_id,
            "case_date": structure.case_date.isoformat(),
            "created_at": structure.created_at.isoformat(),
            "retention_policy": structure.retention_policy.value,
            "backup_level": structure.backup_level.value,
            "retention_expires": self._calculate_retention_expiry(structure).isoformat() if self._calculate_retention_expiry(structure) else None,
            "folder_structure": {
                "case_folder": str(structure.case_folder),
                "evidence_folder": str(structure.evidence_folder),
                "reports_folder": str(structure.reports_folder),
                "analysis_folder": str(structure.analysis_folder),
                "communication_folder": str(structure.communication_folder),
                "audit_folder": str(structure.audit_folder),
                "exports_folder": str(structure.exports_folder),
                "backups_folder": str(structure.backups_folder)
            },
            "compliance": {
                "legal_hold": False,
                "gdpr_compliant": True,
                "audit_trail_enabled": True,
                "encryption_required": True
            }
        }

        metadata_file = structure.case_folder / "folder_metadata.json"
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)

    def _create_folder_documentation(self, structure: FolderStructure) -> None:
        """Create README files for each folder."""
        folder_docs = {
            structure.evidence_folder: """# Evidence Folder

This folder contains all evidence items collected during the investigation.

## Structure:
- Raw evidence files (transactions, addresses, etc.)
- Evidence metadata and chain of custody
- Cryptographic hashes and integrity checks
- Digital signatures for legal admissibility

## Guidelines:
- All evidence must be properly documented
- Chain of custody must be maintained
- Files should be encrypted if containing sensitive data
- Regular integrity checks should be performed
""",
            structure.reports_folder: """# Reports Folder

This folder contains all investigation reports and documentation.

## Structure:
- Victim reports (victim-friendly format)
- Technical reports (detailed analysis)
- Legal reports (court-ready documentation)
- Executive summaries
- Progress reports

## Guidelines:
- Reports should be generated in multiple formats
- Victim reports should be clear and accessible
- Legal reports must meet admissibility standards
- All reports should be digitally signed
""",
            structure.analysis_folder: """# Analysis Folder

This folder contains analysis results and working files.

## Structure:
- Transaction analysis results
- Pattern recognition outputs
- Risk assessment reports
- Visualization files
- Raw analysis data

## Guidelines:
- Analysis should be reproducible
- Methods and parameters should be documented
- Results should be validated
- Backup copies of analysis should be maintained
""",
            structure.communication_folder: """# Communication Folder

This folder contains all communication with the victim and stakeholders.

## Structure:
- Victim communications (emails, messages, calls)
- Status updates and progress reports
- Meeting notes and recordings
- Consent forms and agreements

## Guidelines:
- All communications should be logged
- Sensitive information should be encrypted
- GDPR compliance must be maintained
- Communication preferences should be respected
""",
            structure.audit_folder: """# Audit Folder

This folder contains audit trail and compliance documentation.

## Structure:
- Audit logs and entries
- Access logs and user activities
- Compliance reports
- Security assessments
- Chain of custody documentation

## Guidelines:
- Audit trail must be tamper-evident
- All actions should be logged
- Regular compliance checks should be performed
- Audit data should be backed up regularly
""",
            structure.exports_folder: """# Exports Folder

This folder contains exported data and evidence packages.

## Structure:
- Evidence packages for legal proceedings
- Data exports for other systems
- Backup exports
- Archive packages

## Guidelines:
- Exports should be properly formatted
- Digital signatures should be included
- Export metadata should be maintained
- Regular export validation should be performed
"""
        }

        for folder, content in folder_docs.items():
            readme_file = folder / "README.md"
            with open(readme_file, 'w') as f:
                f.write(content)

    def _calculate_retention_expiry(self, structure: FolderStructure) -> Optional[datetime]:
        """Calculate retention expiry date."""
        retention_days = self.retention_periods.get(structure.retention_policy)
        if retention_days is None:
            return None  # Permanent retention

        return structure.created_at + timedelta(days=retention_days)

    def _setup_retention_policy(self, structure: FolderStructure) -> None:
        """Set up retention policy for folder structure."""
        retention_file = structure.case_folder / "retention_policy.json"

        expiry_date = self._calculate_retention_expiry(structure)

        retention_data = {
            "policy": structure.retention_policy.value,
            "created_at": structure.created_at.isoformat(),
            "expires_at": expiry_date.isoformat() if expiry_date else None,
            "legal_hold": False,
            "auto_delete_enabled": expiry_date is not None,
            "notification_schedule": {
                "90_days_before": True,
                "30_days_before": True,
                "7_days_before": True,
                "1_day_before": True
            }
        }

        with open(retention_file, 'w') as f:
            json.dump(retention_data, f, indent=2)

    def _schedule_backup(self, structure: FolderStructure) -> None:
        """Schedule backup for folder structure."""
        backup_schedule_file = structure.case_folder / "backup_schedule.json"

        backup_interval = self.backup_schedules.get(structure.backup_level, 30)
        next_backup = datetime.now() + timedelta(days=backup_interval)

        schedule_data = {
            "backup_level": structure.backup_level.value,
            "backup_interval_days": backup_interval,
            "next_backup": next_backup.isoformat(),
            "last_backup": None,
            "backup_locations": [
                str(structure.backups_folder),
                str(self.base_backups_dir / structure.victim_id)
            ],
            "backup_enabled": True,
            "compression_enabled": True,
            "encryption_enabled": True
        }

        with open(backup_schedule_file, 'w') as f:
            json.dump(schedule_data, f, indent=2)

    @performance_monitor("backup_creation")
    async def create_backup_async(self, structure: FolderStructure, backup_type: str = "scheduled") -> str:
        """
        Create backup of investigation folder.

        Args:
            structure: Folder structure to backup
            backup_type: Type of backup (scheduled, manual, emergency)

        Returns:
            Path to created backup file
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"backup_{structure.victim_id}_{timestamp}_{backup_type}.tar.gz"
            backup_path = structure.backups_folder / backup_filename

            # Create compressed backup
            with tarfile.open(backup_path, 'w:gz') as tar:
                tar.add(structure.case_folder, arcname=f"{structure.victim_id}_{timestamp}")

            # Create backup metadata
            backup_metadata = {
                "backup_id": f"backup_{timestamp}",
                "victim_id": structure.victim_id,
                "backup_type": backup_type,
                "created_at": datetime.now().isoformat(),
                "backup_size_bytes": backup_path.stat().st_size,
                "source_folder": str(structure.case_folder),
                "backup_file": str(backup_path),
                "compression": "gzip",
                "integrity_hash": self._calculate_file_hash(backup_path)
            }

            metadata_file = structure.backups_folder / f"backup_{timestamp}_metadata.json"
            with open(metadata_file, 'w') as f:
                json.dump(backup_metadata, f, indent=2)

            # Update backup schedule
            await self._update_backup_schedule(structure)

            logger.info(f"Created backup: {backup_path}")
            return str(backup_path)

        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            raise InvestigationError(f"Failed to create backup: {e}")

    async def _update_backup_schedule(self, structure: FolderStructure) -> None:
        """Update backup schedule after successful backup."""
        backup_schedule_file = structure.case_folder / "backup_schedule.json"

        if backup_schedule_file.exists():
            with open(backup_schedule_file, 'r') as f:
                schedule_data = json.load(f)

            backup_interval = self.backup_schedules.get(structure.backup_level, 30)

            schedule_data["last_backup"] = datetime.now().isoformat()
            schedule_data["next_backup"] = (datetime.now() + timedelta(days=backup_interval)).isoformat()

            with open(backup_schedule_file, 'w') as f:
                json.dump(schedule_data, f, indent=2)

    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of file."""
        import hashlib

        hash_sha256 = hashlib.sha256()
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)

        return hash_sha256.hexdigest()

    async def check_retention_policies_async(self) -> List[Dict[str, Any]]:
        """
        Check all investigation folders for retention policy compliance.

        Returns:
            List of folders requiring attention
        """
        try:
            folders_to_review = []

            # Scan all victim folders
            for victim_folder in self.base_investigations_dir.iterdir():
                if not victim_folder.is_dir():
                    continue

                # Scan case folders for each victim
                for case_folder in victim_folder.iterdir():
                    if not case_folder.is_dir():
                        continue

                    retention_file = case_folder / "retention_policy.json"
                    if not retention_file.exists():
                        continue

                    with open(retention_file, 'r') as f:
                        retention_data = json.load(f)

                    # Check if folder is approaching expiry
                    if retention_data.get("expires_at"):
                        expiry_date = TimeUtils.parse_timestamp(retention_data["expires_at"])
                        days_until_expiry = (expiry_date - datetime.now()).days

                        # Check notification thresholds
                        if days_until_expiry <= 90:
                            folders_to_review.append({
                                "victim_id": victim_folder.name,
                                "case_folder": str(case_folder),
                                "retention_policy": retention_data.get("policy"),
                                "expires_at": retention_data.get("expires_at"),
                                "days_until_expiry": days_until_expiry,
                                "legal_hold": retention_data.get("legal_hold", False),
                                "action_required": self._determine_retention_action(days_until_expiry)
                            })

            return folders_to_review

        except Exception as e:
            logger.error(f"Error checking retention policies: {e}")
            return []

    def _determine_retention_action(self, days_until_expiry: int) -> str:
        """Determine required action based on days until expiry."""
        if days_until_expiry <= 1:
            return "immediate_action_required"
        elif days_until_expiry <= 7:
            return "urgent_review_required"
        elif days_until_expiry <= 30:
            return "review_required"
        elif days_until_expiry <= 90:
            return "notification_sent"
        else:
            return "no_action_required"

    async def cleanup_expired_folders_async(self, dry_run: bool = True) -> List[Dict[str, Any]]:
        """
        Clean up expired investigation folders.

        Args:
            dry_run: If True, only simulate cleanup without deleting

        Returns:
            List of folders that were (or would be) cleaned up
        """
        try:
            cleanup_results = []
            folders_to_review = await self.check_retention_policies_async()

            for folder_info in folders_to_review:
                if folder_info["legal_hold"]:
                    continue  # Skip folders on legal hold

                if folder_info["days_until_expiry"] <= 0:
                    case_folder = Path(folder_info["case_folder"])

                    if not dry_run:
                        # Create final backup before deletion
                        structure = self._load_folder_structure(case_folder)
                        if structure:
                            backup_path = await self.create_backup_async(structure, "final_backup")

                            # Move backup to permanent storage
                            permanent_backup_dir = self.base_backups_dir / "expired_cases"
                            permanent_backup_dir.mkdir(parents=True, exist_ok=True)

                            final_backup_path = permanent_backup_dir / Path(backup_path).name
                            shutil.move(backup_path, final_backup_path)

                        # Delete the folder
                        shutil.rmtree(case_folder)

                        cleanup_results.append({
                            **folder_info,
                            "action_taken": "deleted",
                            "backup_created": str(final_backup_path) if structure else None
                        })
                    else:
                        cleanup_results.append({
                            **folder_info,
                            "action_taken": "would_be_deleted",
                            "backup_created": "would_create_backup"
                        })

            return cleanup_results

        except Exception as e:
            logger.error(f"Error cleaning up expired folders: {e}")
            return []

    def _load_folder_structure(self, case_folder: Path) -> Optional[FolderStructure]:
        """Load folder structure from metadata."""
        try:
            metadata_file = case_folder / "folder_metadata.json"
            if not metadata_file.exists():
                return None

            with open(metadata_file, 'r') as f:
                metadata = json.load(f)

            return FolderStructure(
                base_path=self.base_investigations_dir,
                victim_id=metadata["victim_id"],
                case_date=TimeUtils.parse_timestamp(metadata["case_date"]),
                case_folder=Path(),
                evidence_folder=Path(),
                reports_folder=Path(),
                analysis_folder=Path(),
                communication_folder=Path(),
                audit_folder=Path(),
                exports_folder=Path(),
                backups_folder=Path(),
                created_at=TimeUtils.parse_timestamp(metadata["created_at"]),
                retention_policy=RetentionPolicy(metadata["retention_policy"]),
                backup_level=BackupLevel(metadata["backup_level"])
            )

        except Exception as e:
            logger.error(f"Error loading folder structure: {e}")
            return None
