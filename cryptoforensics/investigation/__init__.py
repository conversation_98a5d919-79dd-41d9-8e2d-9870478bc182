"""
Investigation module for CryptoForensics

Provides professional investigation tools including victim fund recovery,
law enforcement integration, victim-centric case management, and
collaborative investigation capabilities.
"""

from .victim_recovery import VictimRecoveryManager, VictimCase, FundMovementAlert, RecoveryStatus, AlertPriority
from .law_enforcement import LawEnforcementIntegration, LECase, LEReport, IntelligenceAlert, CaseClassification, ReportType
from .victim_management import (
    VictimInvestigationManager, VictimProfile, InvestigationCase, CommunicationRecord,
    CaseStatus, CommunicationStatus, DataProtectionLevel
)
from .folder_management import (
    InvestigationFolderManager, FolderStructure, RetentionPolicy, BackupLevel
)

__all__ = [
    # Victim recovery
    "VictimRecoveryManager",
    "VictimCase",
    "FundMovementAlert",
    "RecoveryStatus",
    "AlertPriority",

    # Law enforcement integration
    "LawEnforcementIntegration",
    "LECase",
    "LEReport",
    "IntelligenceAlert",
    "CaseClassification",
    "ReportType",

    # Victim-centric investigation management
    "VictimInvestigationManager",
    "VictimProfile",
    "InvestigationCase",
    "CommunicationRecord",
    "CaseStatus",
    "CommunicationStatus",
    "DataProtectionLevel",

    # Professional folder management
    "InvestigationFolderManager",
    "FolderStructure",
    "RetentionPolicy",
    "BackupLevel"
]
