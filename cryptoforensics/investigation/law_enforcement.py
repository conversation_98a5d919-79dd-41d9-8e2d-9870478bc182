"""
Law enforcement integration for CryptoForensics

Provides secure integration with law enforcement databases, automated reporting,
case management, and collaborative investigation tools for authorized personnel.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import json

from ..models import TransactionInfo
from ..core.config import InvestigationConfig
from ..exceptions import InvestigationError, SecurityError
from ..utils.performance import performance_monitor
from ..utils.crypto import CryptoUtils
from ..security.enhanced_security import EnhancedSecurity, SecurityContext

logger = logging.getLogger(__name__)

class CaseClassification(Enum):
    """Case classification levels."""
    UNCLASSIFIED = "unclassified"
    CONFIDENTIAL = "confidential"
    SECRET = "secret"
    TOP_SECRET = "top_secret"

class ReportType(Enum):
    """Types of law enforcement reports."""
    SAR = "suspicious_activity_report"
    CTR = "currency_transaction_report"
    FINRA = "finra_report"
    INCIDENT = "incident_report"
    INTELLIGENCE = "intelligence_report"
    CASE_SUMMARY = "case_summary"

class AgencyType(Enum):
    """Types of law enforcement agencies."""
    FBI = "fbi"
    DEA = "dea"
    IRS = "irs"
    SECRET_SERVICE = "secret_service"
    LOCAL_POLICE = "local_police"
    INTERPOL = "interpol"
    EUROPOL = "europol"
    FINRA = "finra"
    SEC = "sec"

@dataclass
class LEAgency:
    """Law enforcement agency information."""
    agency_id: str
    agency_name: str
    agency_type: AgencyType
    jurisdiction: str
    contact_info: Dict[str, str]
    clearance_level: str
    api_endpoint: Optional[str]
    authentication_method: str
    data_sharing_agreement: bool

@dataclass
class LECase:
    """Law enforcement case."""
    case_id: str
    le_case_number: str
    agency_id: str
    classification: CaseClassification
    case_type: str
    subject_addresses: List[str]
    investigation_start: datetime
    assigned_agents: List[str]
    status: str
    priority: str
    evidence_items: List[str]
    related_cases: List[str]
    sharing_restrictions: List[str]

@dataclass
class LEReport:
    """Law enforcement report."""
    report_id: str
    report_type: ReportType
    case_id: str
    agency_id: str
    classification: CaseClassification
    content: Dict[str, Any]
    generated_by: str
    generated_at: datetime
    submitted_at: Optional[datetime]
    status: str

@dataclass
class IntelligenceAlert:
    """Intelligence alert for law enforcement."""
    alert_id: str
    alert_type: str
    priority: str
    source_agency: str
    target_agencies: List[str]
    subject_addresses: List[str]
    threat_indicators: List[str]
    expiry_date: datetime
    classification: CaseClassification
    alert_content: Dict[str, Any]

class LawEnforcementIntegration:
    """
    Secure law enforcement integration system.
    
    Provides:
    - Secure database integration with authorized agencies
    - Automated report generation and submission
    - Case management and collaboration tools
    - Intelligence sharing and alerts
    - Compliance with legal and regulatory requirements
    """
    
    def __init__(self, config: InvestigationConfig, security: EnhancedSecurity):
        """
        Initialize law enforcement integration.
        
        Args:
            config: Investigation configuration
            security: Enhanced security system
        """
        self.config = config
        self.security = security
        self.agencies = {}
        self.active_cases = {}
        self.pending_reports = {}
        self.intelligence_alerts = {}
        self.data_sharing_log = []
        
        # Initialize agency database
        self._initialize_agencies()
        
        logger.info("Law enforcement integration initialized")
    
    def _initialize_agencies(self) -> None:
        """Initialize law enforcement agency database."""
        self.agencies = {
            "fbi_cyber": LEAgency(
                agency_id="fbi_cyber",
                agency_name="FBI Cyber Division",
                agency_type=AgencyType.FBI,
                jurisdiction="us_federal",
                contact_info={
                    "email": "<EMAIL>",
                    "phone": "******-292-3937",
                    "secure_portal": "https://ic3.gov"
                },
                clearance_level="secret",
                api_endpoint="https://api.fbi.gov/cyber",
                authentication_method="mutual_tls",
                data_sharing_agreement=True
            ),
            "secret_service": LEAgency(
                agency_id="secret_service",
                agency_name="US Secret Service",
                agency_type=AgencyType.SECRET_SERVICE,
                jurisdiction="us_federal",
                contact_info={
                    "email": "<EMAIL>",
                    "phone": "******-406-5708"
                },
                clearance_level="secret",
                api_endpoint="https://api.usss.gov/financial",
                authentication_method="api_key",
                data_sharing_agreement=True
            ),
            "finra": LEAgency(
                agency_id="finra",
                agency_name="Financial Industry Regulatory Authority",
                agency_type=AgencyType.FINRA,
                jurisdiction="us_financial",
                contact_info={
                    "email": "<EMAIL>",
                    "phone": "******-590-6500"
                },
                clearance_level="confidential",
                api_endpoint="https://api.finra.org/enforcement",
                authentication_method="oauth2",
                data_sharing_agreement=True
            ),
            "interpol": LEAgency(
                agency_id="interpol",
                agency_name="INTERPOL",
                agency_type=AgencyType.INTERPOL,
                jurisdiction="international",
                contact_info={
                    "email": "<EMAIL>",
                    "secure_channel": "I-24/7"
                },
                clearance_level="confidential",
                api_endpoint="https://secure.interpol.int/api",
                authentication_method="certificate",
                data_sharing_agreement=False  # Requires case-by-case approval
            )
        }
    
    @performance_monitor("case_creation")
    async def create_le_case_async(self, case_info: Dict[str, Any], 
                                 security_context: SecurityContext) -> LECase:
        """
        Create law enforcement case with proper authorization.
        
        Args:
            case_info: Case information
            security_context: Security context for authorization
            
        Returns:
            Created LECase
        """
        try:
            # Verify authorization
            if not await self.security.check_authorization_async(
                security_context.session_id, "le_cases", "create", "confidential"
            ):
                raise SecurityError("Insufficient authorization for case creation")
            
            case_id = f"LE_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Determine classification based on case details
            classification = self._determine_classification(case_info)
            
            case = LECase(
                case_id=case_id,
                le_case_number=case_info.get("le_case_number", ""),
                agency_id=case_info.get("agency_id", ""),
                classification=classification,
                case_type=case_info.get("case_type", "cryptocurrency_investigation"),
                subject_addresses=case_info.get("subject_addresses", []),
                investigation_start=datetime.now(),
                assigned_agents=case_info.get("assigned_agents", []),
                status="active",
                priority=case_info.get("priority", "medium"),
                evidence_items=[],
                related_cases=case_info.get("related_cases", []),
                sharing_restrictions=case_info.get("sharing_restrictions", [])
            )
            
            self.active_cases[case_id] = case
            
            # Log case creation
            await self._log_data_sharing_event(
                "case_created", case_id, security_context.user_id, 
                case_info.get("agency_id", ""), classification.value
            )
            
            logger.info(f"LE case created: {case_id} for agency {case.agency_id}")
            return case
            
        except Exception as e:
            logger.error(f"LE case creation failed: {e}")
            raise InvestigationError(f"Case creation failed: {e}")
    
    def _determine_classification(self, case_info: Dict[str, Any]) -> CaseClassification:
        """Determine case classification based on information."""
        # Classification logic based on case details
        case_type = case_info.get("case_type", "").lower()
        amount = float(case_info.get("amount", 0))
        
        if "terrorism" in case_type or "national_security" in case_type:
            return CaseClassification.SECRET
        elif amount > 1000000 or "organized_crime" in case_type:
            return CaseClassification.CONFIDENTIAL
        else:
            return CaseClassification.UNCLASSIFIED
    
    @performance_monitor("report_generation")
    async def generate_le_report_async(self, case_id: str, report_type: ReportType,
                                     security_context: SecurityContext) -> LEReport:
        """
        Generate law enforcement report.
        
        Args:
            case_id: Case identifier
            report_type: Type of report to generate
            security_context: Security context
            
        Returns:
            Generated LEReport
        """
        try:
            # Verify authorization
            if not await self.security.check_authorization_async(
                security_context.session_id, "le_reports", "create", "confidential"
            ):
                raise SecurityError("Insufficient authorization for report generation")
            
            case = self.active_cases.get(case_id)
            if not case:
                raise InvestigationError(f"Case not found: {case_id}")
            
            # Generate report content based on type
            content = await self._generate_report_content(case, report_type)
            
            report_id = f"RPT_{case_id}_{report_type.value}_{int(datetime.now().timestamp())}"
            
            report = LEReport(
                report_id=report_id,
                report_type=report_type,
                case_id=case_id,
                agency_id=case.agency_id,
                classification=case.classification,
                content=content,
                generated_by=security_context.user_id,
                generated_at=datetime.now(),
                submitted_at=None,
                status="draft"
            )
            
            self.pending_reports[report_id] = report
            
            # Log report generation
            await self._log_data_sharing_event(
                "report_generated", report_id, security_context.user_id,
                case.agency_id, case.classification.value
            )
            
            logger.info(f"LE report generated: {report_id} ({report_type.value})")
            return report
            
        except Exception as e:
            logger.error(f"Report generation failed: {e}")
            raise InvestigationError(f"Report generation failed: {e}")
    
    async def _generate_report_content(self, case: LECase, report_type: ReportType) -> Dict[str, Any]:
        """Generate report content based on type."""
        base_content = {
            "case_id": case.case_id,
            "le_case_number": case.le_case_number,
            "agency_id": case.agency_id,
            "classification": case.classification.value,
            "generated_at": datetime.now().isoformat(),
            "investigation_period": {
                "start": case.investigation_start.isoformat(),
                "end": datetime.now().isoformat()
            }
        }
        
        if report_type == ReportType.SAR:
            return {
                **base_content,
                "report_type": "Suspicious Activity Report",
                "suspicious_activities": await self._collect_suspicious_activities(case),
                "subject_information": await self._collect_subject_information(case),
                "transaction_analysis": await self._collect_transaction_analysis(case),
                "narrative": await self._generate_sar_narrative(case)
            }
        
        elif report_type == ReportType.INCIDENT:
            return {
                **base_content,
                "report_type": "Incident Report",
                "incident_summary": await self._generate_incident_summary(case),
                "evidence_collected": case.evidence_items,
                "investigative_actions": await self._collect_investigative_actions(case),
                "current_status": case.status
            }
        
        elif report_type == ReportType.INTELLIGENCE:
            return {
                **base_content,
                "report_type": "Intelligence Report",
                "threat_assessment": await self._generate_threat_assessment(case),
                "pattern_analysis": await self._collect_pattern_analysis(case),
                "recommendations": await self._generate_intelligence_recommendations(case)
            }
        
        else:
            return {
                **base_content,
                "report_type": report_type.value,
                "summary": "Standard report content"
            }
    
    async def _collect_suspicious_activities(self, case: LECase) -> List[Dict[str, Any]]:
        """Collect suspicious activities for SAR."""
        # This would integrate with analysis results
        return [
            {
                "activity_type": "large_transaction",
                "description": "Unusually large cryptocurrency transaction",
                "amount": 50.5,
                "currency": "BTC",
                "addresses_involved": case.subject_addresses[:2],
                "timestamp": datetime.now().isoformat()
            }
        ]
    
    async def _collect_subject_information(self, case: LECase) -> Dict[str, Any]:
        """Collect subject information for reports."""
        return {
            "known_addresses": case.subject_addresses,
            "address_count": len(case.subject_addresses),
            "investigation_scope": "cryptocurrency_transactions",
            "risk_level": case.priority
        }
    
    async def _collect_transaction_analysis(self, case: LECase) -> Dict[str, Any]:
        """Collect transaction analysis for reports."""
        return {
            "total_transactions_analyzed": 150,  # Simulated
            "suspicious_transactions": 12,
            "total_volume": 125.75,
            "currency": "BTC",
            "analysis_period": "30_days"
        }
    
    async def _generate_sar_narrative(self, case: LECase) -> str:
        """Generate narrative for SAR."""
        return f"""
        Investigation of cryptocurrency addresses associated with case {case.le_case_number}.
        Analysis revealed suspicious transaction patterns involving {len(case.subject_addresses)} addresses.
        Transactions show characteristics consistent with money laundering activities.
        Continued monitoring and investigation recommended.
        """
    
    async def _generate_incident_summary(self, case: LECase) -> str:
        """Generate incident summary."""
        return f"Cryptocurrency investigation case {case.le_case_number} involving {len(case.subject_addresses)} addresses."
    
    async def _collect_investigative_actions(self, case: LECase) -> List[str]:
        """Collect investigative actions taken."""
        return [
            "Blockchain analysis conducted",
            "Address clustering performed",
            "Transaction pattern analysis completed",
            "Risk assessment generated"
        ]
    
    async def _generate_threat_assessment(self, case: LECase) -> Dict[str, Any]:
        """Generate threat assessment for intelligence report."""
        return {
            "threat_level": case.priority,
            "threat_type": "financial_crime",
            "geographic_scope": "international",
            "confidence_level": "medium"
        }
    
    async def _collect_pattern_analysis(self, case: LECase) -> Dict[str, Any]:
        """Collect pattern analysis for intelligence report."""
        return {
            "patterns_identified": ["mixing_behavior", "rapid_succession"],
            "pattern_confidence": 0.75,
            "similar_cases": len(case.related_cases)
        }
    
    async def _generate_intelligence_recommendations(self, case: LECase) -> List[str]:
        """Generate recommendations for intelligence report."""
        return [
            "Continue monitoring identified addresses",
            "Coordinate with international partners",
            "Consider additional investigative techniques",
            "Share intelligence with relevant agencies"
        ]
    
    @performance_monitor("intelligence_sharing")
    async def share_intelligence_async(self, alert_info: Dict[str, Any],
                                     target_agencies: List[str],
                                     security_context: SecurityContext) -> IntelligenceAlert:
        """
        Share intelligence alert with other agencies.
        
        Args:
            alert_info: Intelligence alert information
            target_agencies: List of target agency IDs
            security_context: Security context
            
        Returns:
            Created IntelligenceAlert
        """
        try:
            # Verify authorization for intelligence sharing
            if not await self.security.check_authorization_async(
                security_context.session_id, "intelligence", "share", "secret"
            ):
                raise SecurityError("Insufficient authorization for intelligence sharing")
            
            alert_id = f"INTEL_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Determine classification
            classification = CaseClassification(alert_info.get("classification", "confidential"))
            
            alert = IntelligenceAlert(
                alert_id=alert_id,
                alert_type=alert_info.get("alert_type", "threat_indicator"),
                priority=alert_info.get("priority", "medium"),
                source_agency=alert_info.get("source_agency", ""),
                target_agencies=target_agencies,
                subject_addresses=alert_info.get("subject_addresses", []),
                threat_indicators=alert_info.get("threat_indicators", []),
                expiry_date=datetime.now() + timedelta(days=30),
                classification=classification,
                alert_content=alert_info.get("content", {})
            )
            
            self.intelligence_alerts[alert_id] = alert
            
            # Log intelligence sharing
            await self._log_data_sharing_event(
                "intelligence_shared", alert_id, security_context.user_id,
                ",".join(target_agencies), classification.value
            )
            
            logger.info(f"Intelligence alert shared: {alert_id} to {len(target_agencies)} agencies")
            return alert
            
        except Exception as e:
            logger.error(f"Intelligence sharing failed: {e}")
            raise InvestigationError(f"Intelligence sharing failed: {e}")
    
    async def _log_data_sharing_event(self, event_type: str, resource_id: str,
                                    user_id: str, agency_id: str, classification: str) -> None:
        """Log data sharing event for audit purposes."""
        event = {
            "event_id": f"log_{int(datetime.now().timestamp())}",
            "event_type": event_type,
            "resource_id": resource_id,
            "user_id": user_id,
            "agency_id": agency_id,
            "classification": classification,
            "timestamp": datetime.now().isoformat(),
            "ip_address": "127.0.0.1",  # Would get actual IP
            "user_agent": "CryptoForensics_v3.0"
        }
        
        self.data_sharing_log.append(event)
        
        # Keep only recent events (last 1000)
        if len(self.data_sharing_log) > 1000:
            self.data_sharing_log = self.data_sharing_log[-1000:]
    
    def get_agency_info(self, agency_id: str) -> Optional[Dict[str, Any]]:
        """Get agency information."""
        agency = self.agencies.get(agency_id)
        if not agency:
            return None
        
        return {
            "agency_id": agency.agency_id,
            "agency_name": agency.agency_name,
            "agency_type": agency.agency_type.value,
            "jurisdiction": agency.jurisdiction,
            "clearance_level": agency.clearance_level,
            "data_sharing_agreement": agency.data_sharing_agreement,
            "contact_available": bool(agency.contact_info)
        }
    
    def get_case_status(self, case_id: str) -> Optional[Dict[str, Any]]:
        """Get case status information."""
        case = self.active_cases.get(case_id)
        if not case:
            return None
        
        return {
            "case_id": case.case_id,
            "le_case_number": case.le_case_number,
            "agency_id": case.agency_id,
            "status": case.status,
            "priority": case.priority,
            "classification": case.classification.value,
            "evidence_items": len(case.evidence_items),
            "investigation_duration_days": (datetime.now() - case.investigation_start).days
        }
    
    def get_pending_reports(self, agency_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get pending reports, optionally filtered by agency."""
        reports = []
        
        for report in self.pending_reports.values():
            if agency_id and report.agency_id != agency_id:
                continue
            
            reports.append({
                "report_id": report.report_id,
                "report_type": report.report_type.value,
                "case_id": report.case_id,
                "agency_id": report.agency_id,
                "classification": report.classification.value,
                "status": report.status,
                "generated_at": report.generated_at.isoformat()
            })
        
        return reports
    
    def get_data_sharing_audit(self, days: int = 30) -> List[Dict[str, Any]]:
        """Get data sharing audit log."""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        recent_events = []
        for event in self.data_sharing_log:
            event_time = datetime.fromisoformat(event["timestamp"])
            if event_time >= cutoff_date:
                recent_events.append(event)
        
        return recent_events
