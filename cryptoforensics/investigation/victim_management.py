"""
Comprehensive victim-centric investigation management system for CryptoForensics v3.0

Provides professional victim management with secure data handling, case tracking,
folder organization, and integration with existing investigation workflow.
"""

import asyncio
import logging
import json
import uuid
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field, asdict
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from pathlib import Path

from ..models import TransactionInfo
from ..core.config import InvestigationConfig
from ..exceptions import InvestigationError
from ..utils.performance import performance_monitor
from ..utils.time_utils import TimeUtils
from ..utils.crypto import CryptoUtils
from ..security.enhanced_security import EnhancedSecurity
from .victim_recovery import VictimCase, RecoveryStatus, AlertPriority

logger = logging.getLogger(__name__)

class CaseStatus(Enum):
    """Investigation case status."""
    INTAKE = "intake"
    ACTIVE = "active"
    ANALYSIS = "analysis"
    REPORTING = "reporting"
    COMPLETED = "completed"
    CLOSED = "closed"
    SUSPENDED = "suspended"

class CommunicationStatus(Enum):
    """Victim communication status."""
    INITIAL_CONTACT = "initial_contact"
    INFORMATION_GATHERING = "information_gathering"
    REGULAR_UPDATES = "regular_updates"
    FINAL_REPORT = "final_report"
    CASE_CLOSED = "case_closed"

class DataProtectionLevel(Enum):
    """Data protection levels for victim information."""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"

@dataclass
class VictimProfile:
    """Comprehensive victim profile with secure data handling."""
    victim_id: str

    # Personal Information (encrypted)
    full_name: str
    email: str
    phone: Optional[str] = None
    address: Optional[str] = None

    # Case Information
    preferred_contact_method: str = "email"
    timezone: str = "UTC"
    language: str = "en"

    # Investigation Details
    affected_addresses: List[str] = field(default_factory=list)
    incident_description: str = ""
    estimated_loss: float = 0.0
    currency: str = "BTC"
    incident_date: Optional[datetime] = None

    # Case Management
    assigned_investigator: str = "unassigned"
    case_priority: AlertPriority = AlertPriority.MEDIUM
    communication_status: CommunicationStatus = CommunicationStatus.INITIAL_CONTACT
    data_protection_level: DataProtectionLevel = DataProtectionLevel.CONFIDENTIAL

    # Metadata
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    created_by: str = "system"
    gdpr_consent: bool = False
    data_retention_date: Optional[datetime] = None

    # Communication History
    communication_log: List[Dict[str, Any]] = field(default_factory=list)

    def __post_init__(self):
        """Post-initialization processing."""
        if not self.victim_id:
            self.victim_id = f"VIC_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"

        if self.data_retention_date is None:
            # Default 7-year retention for legal compliance
            self.data_retention_date = datetime.now() + timedelta(days=7*365)

@dataclass
class InvestigationCase:
    """Comprehensive investigation case management."""
    case_id: str
    victim_id: str

    # Case Details
    case_title: str
    case_description: str
    case_status: CaseStatus = CaseStatus.INTAKE
    case_priority: AlertPriority = AlertPriority.MEDIUM

    # Investigation Parameters
    target_addresses: List[str] = field(default_factory=list)
    investigation_depth: int = 3
    investigation_scope: str = "standard"

    # Case Management
    assigned_investigator: str = "unassigned"
    supervisor: Optional[str] = None
    case_folder_path: Optional[str] = None

    # Progress Tracking
    progress_percentage: float = 0.0
    milestones_completed: List[str] = field(default_factory=list)
    next_actions: List[str] = field(default_factory=list)

    # Evidence and Results
    evidence_items: List[str] = field(default_factory=list)
    analysis_results: Dict[str, Any] = field(default_factory=dict)
    report_paths: List[str] = field(default_factory=list)

    # Timestamps
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    # Integration with existing systems
    recovery_case_id: Optional[str] = None  # Link to VictimRecoveryManager
    investigation_id: Optional[str] = None  # Link to CryptoForensicsInvestigator

    def __post_init__(self):
        """Post-initialization processing."""
        if not self.case_id:
            self.case_id = f"CASE_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"

@dataclass
class CommunicationRecord:
    """Record of communication with victim."""
    record_id: str
    victim_id: str
    case_id: str

    # Communication Details
    communication_type: str  # "email", "phone", "meeting", "report"
    direction: str  # "outbound", "inbound"
    subject: str
    content: str

    # Metadata
    timestamp: datetime = field(default_factory=datetime.now)
    sent_by: str = "system"
    delivery_status: str = "pending"
    read_receipt: bool = False

    # Attachments and References
    attachments: List[str] = field(default_factory=list)
    related_evidence: List[str] = field(default_factory=list)

    def __post_init__(self):
        """Post-initialization processing."""
        if not self.record_id:
            self.record_id = f"COMM_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"

class VictimInvestigationManager:
    """
    Comprehensive victim-centric investigation management system.

    Provides professional victim management with:
    - Secure victim profile management
    - Case tracking and progress monitoring
    - Professional folder organization
    - Victim communication management
    - Integration with existing v3.0 systems
    - GDPR compliance and data protection
    """

    def __init__(self, config: InvestigationConfig, security: Optional[EnhancedSecurity] = None):
        """
        Initialize victim investigation manager.

        Args:
            config: Investigation configuration
            security: Enhanced security instance for data protection
        """
        self.config = config
        self.security = security

        # Data storage
        self.victim_profiles: Dict[str, VictimProfile] = {}
        self.investigation_cases: Dict[str, InvestigationCase] = {}
        self.communication_records: Dict[str, List[CommunicationRecord]] = {}

        # Base directories
        self.base_investigations_dir = Path(config.output_directory) / "investigations"
        self.base_investigations_dir.mkdir(parents=True, exist_ok=True)

        # Data protection
        self.encryption_key = None
        if security:
            self.encryption_key = security.generate_key()

        logger.info("Victim investigation manager initialized")

    def _create_case_folder_structure(self, victim_id: str, case_date: datetime) -> Path:
        """
        Create professional investigation folder structure.

        Structure: /investigations/{victim_id}/{case_date}/
        - evidence/
        - reports/
        - analysis/
        - communication/
        - audit/

        Args:
            victim_id: Victim identifier
            case_date: Case creation date

        Returns:
            Path to case folder
        """
        case_date_str = case_date.strftime('%Y%m%d')
        case_folder = self.base_investigations_dir / victim_id / case_date_str

        # Create folder structure
        subfolders = ['evidence', 'reports', 'analysis', 'communication', 'audit', 'exports']
        for subfolder in subfolders:
            (case_folder / subfolder).mkdir(parents=True, exist_ok=True)

        # Create case metadata file
        metadata = {
            "victim_id": victim_id,
            "case_date": case_date.isoformat(),
            "folder_created": datetime.now().isoformat(),
            "structure_version": "3.0",
            "compliance_level": "professional"
        }

        with open(case_folder / "case_metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)

        logger.info(f"Created case folder structure: {case_folder}")
        return case_folder

    @performance_monitor("victim_profile_creation")
    async def create_victim_profile_async(self, victim_data: Dict[str, Any]) -> VictimProfile:
        """
        Create new victim profile with secure data handling.

        Args:
            victim_data: Victim information dictionary

        Returns:
            Created VictimProfile
        """
        try:
            # Create victim profile
            profile = VictimProfile(
                victim_id=victim_data.get("victim_id", ""),
                full_name=victim_data.get("full_name", ""),
                email=victim_data.get("email", ""),
                phone=victim_data.get("phone"),
                address=victim_data.get("address"),
                preferred_contact_method=victim_data.get("preferred_contact_method", "email"),
                timezone=victim_data.get("timezone", "UTC"),
                language=victim_data.get("language", "en"),
                affected_addresses=victim_data.get("affected_addresses", []),
                incident_description=victim_data.get("incident_description", ""),
                estimated_loss=float(victim_data.get("estimated_loss", 0.0)),
                currency=victim_data.get("currency", "BTC"),
                incident_date=TimeUtils.parse_timestamp(victim_data.get("incident_date", "")),
                assigned_investigator=victim_data.get("assigned_investigator", "unassigned"),
                gdpr_consent=victim_data.get("gdpr_consent", False),
                created_by=victim_data.get("created_by", "system")
            )

            # Store profile securely
            self.victim_profiles[profile.victim_id] = profile
            await self._save_victim_profile_async(profile)

            logger.info(f"Created victim profile: {profile.victim_id}")
            return profile

        except Exception as e:
            logger.error(f"Error creating victim profile: {e}")
            raise InvestigationError(f"Failed to create victim profile: {e}")

    @performance_monitor("case_creation")
    async def create_investigation_case_async(self, victim_id: str, case_data: Dict[str, Any]) -> InvestigationCase:
        """
        Create new investigation case for victim.

        Args:
            victim_id: Victim identifier
            case_data: Case information dictionary

        Returns:
            Created InvestigationCase
        """
        try:
            # Verify victim exists
            if victim_id not in self.victim_profiles:
                raise InvestigationError(f"Victim profile not found: {victim_id}")

            # Create case folder structure
            case_date = datetime.now()
            case_folder = self._create_case_folder_structure(victim_id, case_date)

            # Create investigation case
            case = InvestigationCase(
                case_id=case_data.get("case_id", ""),
                victim_id=victim_id,
                case_title=case_data.get("case_title", f"Investigation for {victim_id}"),
                case_description=case_data.get("case_description", ""),
                case_priority=AlertPriority(case_data.get("case_priority", "medium")),
                target_addresses=case_data.get("target_addresses", []),
                investigation_depth=case_data.get("investigation_depth", 3),
                investigation_scope=case_data.get("investigation_scope", "standard"),
                assigned_investigator=case_data.get("assigned_investigator", "unassigned"),
                supervisor=case_data.get("supervisor"),
                case_folder_path=str(case_folder),
                started_at=datetime.now()
            )

            # Store case
            self.investigation_cases[case.case_id] = case
            await self._save_investigation_case_async(case)

            # Initialize communication log for victim
            if victim_id not in self.communication_records:
                self.communication_records[victim_id] = []

            # Log case creation
            await self._log_communication_async(
                victim_id=victim_id,
                case_id=case.case_id,
                communication_type="system",
                direction="outbound",
                subject="Investigation Case Created",
                content=f"Investigation case {case.case_id} created for victim {victim_id}",
                sent_by=case_data.get("created_by", "system")
            )

            logger.info(f"Created investigation case: {case.case_id} for victim: {victim_id}")
            return case

        except Exception as e:
            logger.error(f"Error creating investigation case: {e}")
            raise InvestigationError(f"Failed to create investigation case: {e}")

    async def _save_victim_profile_async(self, profile: VictimProfile) -> None:
        """Save victim profile securely to disk."""
        try:
            profile_data = asdict(profile)

            # Create victim directory
            victim_dir = self.base_investigations_dir / profile.victim_id
            victim_dir.mkdir(parents=True, exist_ok=True)

            # Encrypt sensitive data if security is enabled
            if self.security and self.encryption_key:
                # Separate sensitive and non-sensitive data
                sensitive_fields = ['full_name', 'email', 'phone', 'address', 'communication_log']
                sensitive_data = {k: v for k, v in profile_data.items() if k in sensitive_fields}
                non_sensitive_data = {k: v for k, v in profile_data.items() if k not in sensitive_fields}

                # Encrypt sensitive data
                encrypted_data = CryptoUtils.encrypt_data(sensitive_data, self.encryption_key)

                # Save encrypted sensitive data
                with open(victim_dir / "profile_sensitive.enc", 'wb') as f:
                    f.write(encrypted_data)

                # Save non-sensitive data as JSON
                with open(victim_dir / "profile_metadata.json", 'w') as f:
                    json.dump(non_sensitive_data, f, indent=2, default=str)
            else:
                # Save as plain JSON if encryption not available
                with open(victim_dir / "profile.json", 'w') as f:
                    json.dump(profile_data, f, indent=2, default=str)

            logger.debug(f"Saved victim profile: {profile.victim_id}")

        except Exception as e:
            logger.error(f"Error saving victim profile: {e}")
            raise InvestigationError(f"Failed to save victim profile: {e}")

    async def _save_investigation_case_async(self, case: InvestigationCase) -> None:
        """Save investigation case to disk."""
        try:
            case_data = asdict(case)

            # Save to case folder
            case_folder = Path(case.case_folder_path)
            with open(case_folder / "case_details.json", 'w') as f:
                json.dump(case_data, f, indent=2, default=str)

            logger.debug(f"Saved investigation case: {case.case_id}")

        except Exception as e:
            logger.error(f"Error saving investigation case: {e}")
            raise InvestigationError(f"Failed to save investigation case: {e}")

    async def _log_communication_async(self, victim_id: str, case_id: str,
                                     communication_type: str, direction: str,
                                     subject: str, content: str, sent_by: str = "system",
                                     attachments: Optional[List[str]] = None) -> CommunicationRecord:
        """Log communication with victim."""
        try:
            record = CommunicationRecord(
                record_id="",
                victim_id=victim_id,
                case_id=case_id,
                communication_type=communication_type,
                direction=direction,
                subject=subject,
                content=content,
                sent_by=sent_by,
                attachments=attachments or []
            )

            # Add to communication log
            if victim_id not in self.communication_records:
                self.communication_records[victim_id] = []
            self.communication_records[victim_id].append(record)

            # Save communication record
            await self._save_communication_record_async(record)

            logger.debug(f"Logged communication: {record.record_id}")
            return record

        except Exception as e:
            logger.error(f"Error logging communication: {e}")
            raise InvestigationError(f"Failed to log communication: {e}")

    async def _save_communication_record_async(self, record: CommunicationRecord) -> None:
        """Save communication record to disk."""
        try:
            # Find case folder
            case = self.investigation_cases.get(record.case_id)
            if not case:
                logger.warning(f"Case not found for communication record: {record.case_id}")
                return

            case_folder = Path(case.case_folder_path)
            comm_folder = case_folder / "communication"

            # Save record
            record_data = asdict(record)
            filename = f"comm_{record.record_id}.json"

            with open(comm_folder / filename, 'w') as f:
                json.dump(record_data, f, indent=2, default=str)

            logger.debug(f"Saved communication record: {record.record_id}")

        except Exception as e:
            logger.error(f"Error saving communication record: {e}")
            raise InvestigationError(f"Failed to save communication record: {e}")

    async def update_case_status_async(self, case_id: str, new_status: CaseStatus,
                                     notes: Optional[str] = None, updated_by: str = "system") -> None:
        """Update investigation case status."""
        try:
            case = self.investigation_cases.get(case_id)
            if not case:
                raise InvestigationError(f"Case not found: {case_id}")

            old_status = case.case_status
            case.case_status = new_status
            case.updated_at = datetime.now()

            # Update progress percentage based on status
            status_progress = {
                CaseStatus.INTAKE: 10,
                CaseStatus.ACTIVE: 30,
                CaseStatus.ANALYSIS: 60,
                CaseStatus.REPORTING: 80,
                CaseStatus.COMPLETED: 100,
                CaseStatus.CLOSED: 100,
                CaseStatus.SUSPENDED: case.progress_percentage  # Keep current
            }
            case.progress_percentage = status_progress.get(new_status, case.progress_percentage)

            # Set completion timestamp if completed
            if new_status in [CaseStatus.COMPLETED, CaseStatus.CLOSED]:
                case.completed_at = datetime.now()

            # Save updated case
            await self._save_investigation_case_async(case)

            # Log status change
            await self._log_communication_async(
                victim_id=case.victim_id,
                case_id=case_id,
                communication_type="system",
                direction="outbound",
                subject=f"Case Status Updated: {new_status.value.title()}",
                content=f"Case status changed from {old_status.value} to {new_status.value}. {notes or ''}",
                sent_by=updated_by
            )

            logger.info(f"Updated case status: {case_id} -> {new_status.value}")

        except Exception as e:
            logger.error(f"Error updating case status: {e}")
            raise InvestigationError(f"Failed to update case status: {e}")

    async def get_victim_profile_async(self, victim_id: str) -> Optional[VictimProfile]:
        """Get victim profile by ID."""
        try:
            profile = self.victim_profiles.get(victim_id)
            if not profile:
                # Try to load from disk
                profile = await self._load_victim_profile_async(victim_id)
                if profile:
                    self.victim_profiles[victim_id] = profile

            return profile

        except Exception as e:
            logger.error(f"Error getting victim profile: {e}")
            return None

    async def get_investigation_case_async(self, case_id: str) -> Optional[InvestigationCase]:
        """Get investigation case by ID."""
        try:
            case = self.investigation_cases.get(case_id)
            if not case:
                # Try to load from disk
                case = await self._load_investigation_case_async(case_id)
                if case:
                    self.investigation_cases[case_id] = case

            return case

        except Exception as e:
            logger.error(f"Error getting investigation case: {e}")
            return None

    async def list_victim_cases_async(self, victim_id: str) -> List[InvestigationCase]:
        """List all cases for a victim."""
        try:
            cases = []
            for case in self.investigation_cases.values():
                if case.victim_id == victim_id:
                    cases.append(case)

            # Sort by creation date (newest first)
            cases.sort(key=lambda x: x.created_at, reverse=True)
            return cases

        except Exception as e:
            logger.error(f"Error listing victim cases: {e}")
            return []

    async def get_case_communication_history_async(self, case_id: str) -> List[CommunicationRecord]:
        """Get communication history for a case."""
        try:
            case = await self.get_investigation_case_async(case_id)
            if not case:
                return []

            victim_records = self.communication_records.get(case.victim_id, [])
            case_records = [r for r in victim_records if r.case_id == case_id]

            # Sort by timestamp (newest first)
            case_records.sort(key=lambda x: x.timestamp, reverse=True)
            return case_records

        except Exception as e:
            logger.error(f"Error getting communication history: {e}")
            return []

    async def _load_victim_profile_async(self, victim_id: str) -> Optional[VictimProfile]:
        """Load victim profile from disk."""
        try:
            victim_dir = self.base_investigations_dir / victim_id
            if not victim_dir.exists():
                return None

            # Try to load encrypted profile first
            if (victim_dir / "profile_sensitive.enc").exists() and (victim_dir / "profile_metadata.json").exists():
                # Load non-sensitive data
                with open(victim_dir / "profile_metadata.json", 'r') as f:
                    non_sensitive_data = json.load(f)

                # Load and decrypt sensitive data
                if self.security and self.encryption_key:
                    with open(victim_dir / "profile_sensitive.enc", 'rb') as f:
                        encrypted_data = f.read()

                    sensitive_data = CryptoUtils.decrypt_data(encrypted_data, self.encryption_key)

                    # Combine data
                    profile_data = {**non_sensitive_data, **sensitive_data}
                else:
                    logger.warning(f"Cannot decrypt profile data for {victim_id} - no encryption key")
                    return None

            # Try plain JSON profile
            elif (victim_dir / "profile.json").exists():
                with open(victim_dir / "profile.json", 'r') as f:
                    profile_data = json.load(f)
            else:
                return None

            # Convert timestamps
            for field in ['created_at', 'updated_at', 'incident_date', 'data_retention_date']:
                if field in profile_data and profile_data[field]:
                    profile_data[field] = TimeUtils.parse_timestamp(profile_data[field])

            # Create profile object
            profile = VictimProfile(**profile_data)
            logger.debug(f"Loaded victim profile: {victim_id}")
            return profile

        except Exception as e:
            logger.error(f"Error loading victim profile: {e}")
            return None

    async def _load_investigation_case_async(self, case_id: str) -> Optional[InvestigationCase]:
        """Load investigation case from disk."""
        try:
            # Search for case in all victim directories
            for victim_dir in self.base_investigations_dir.iterdir():
                if not victim_dir.is_dir():
                    continue

                # Search in date directories
                for date_dir in victim_dir.iterdir():
                    if not date_dir.is_dir():
                        continue

                    case_file = date_dir / "case_details.json"
                    if case_file.exists():
                        with open(case_file, 'r') as f:
                            case_data = json.load(f)

                        if case_data.get("case_id") == case_id:
                            # Convert timestamps
                            for field in ['created_at', 'updated_at', 'started_at', 'completed_at']:
                                if field in case_data and case_data[field]:
                                    case_data[field] = TimeUtils.parse_timestamp(case_data[field])

                            # Convert enums
                            if 'case_status' in case_data:
                                case_data['case_status'] = CaseStatus(case_data['case_status'])
                            if 'case_priority' in case_data:
                                case_data['case_priority'] = AlertPriority(case_data['case_priority'])

                            case = InvestigationCase(**case_data)
                            logger.debug(f"Loaded investigation case: {case_id}")
                            return case

            return None

        except Exception as e:
            logger.error(f"Error loading investigation case: {e}")
            return None

    async def send_victim_update_async(self, victim_id: str, case_id: str,
                                     update_type: str, message: str,
                                     attachments: Optional[List[str]] = None,
                                     sent_by: str = "system") -> CommunicationRecord:
        """Send update to victim."""
        try:
            # Verify victim and case exist
            victim = await self.get_victim_profile_async(victim_id)
            case = await self.get_investigation_case_async(case_id)

            if not victim:
                raise InvestigationError(f"Victim not found: {victim_id}")
            if not case:
                raise InvestigationError(f"Case not found: {case_id}")

            # Create communication record
            record = await self._log_communication_async(
                victim_id=victim_id,
                case_id=case_id,
                communication_type=victim.preferred_contact_method,
                direction="outbound",
                subject=f"Case Update: {update_type}",
                content=message,
                sent_by=sent_by,
                attachments=attachments
            )

            # Update victim communication status
            victim.communication_status = CommunicationStatus.REGULAR_UPDATES
            victim.updated_at = datetime.now()
            await self._save_victim_profile_async(victim)

            logger.info(f"Sent victim update: {victim_id} - {update_type}")
            return record

        except Exception as e:
            logger.error(f"Error sending victim update: {e}")
            raise InvestigationError(f"Failed to send victim update: {e}")

    def generate_case_summary(self, case_id: str) -> Dict[str, Any]:
        """Generate comprehensive case summary."""
        try:
            case = self.investigation_cases.get(case_id)
            if not case:
                return {"error": f"Case not found: {case_id}"}

            victim = self.victim_profiles.get(case.victim_id)
            if not victim:
                return {"error": f"Victim not found: {case.victim_id}"}

            # Calculate case duration
            duration = None
            if case.started_at:
                end_time = case.completed_at or datetime.now()
                duration = (end_time - case.started_at).total_seconds() / 3600  # hours

            # Get communication stats
            comm_records = self.communication_records.get(case.victim_id, [])
            case_comms = [r for r in comm_records if r.case_id == case_id]

            return {
                "case_id": case.case_id,
                "victim_id": case.victim_id,
                "case_title": case.case_title,
                "status": case.case_status.value,
                "priority": case.case_priority.value,
                "progress_percentage": case.progress_percentage,
                "assigned_investigator": case.assigned_investigator,
                "supervisor": case.supervisor,
                "created_at": case.created_at.isoformat(),
                "started_at": case.started_at.isoformat() if case.started_at else None,
                "completed_at": case.completed_at.isoformat() if case.completed_at else None,
                "duration_hours": duration,
                "victim_info": {
                    "victim_id": victim.victim_id,
                    "estimated_loss": victim.estimated_loss,
                    "currency": victim.currency,
                    "incident_date": victim.incident_date.isoformat() if victim.incident_date else None,
                    "affected_addresses_count": len(victim.affected_addresses),
                    "communication_status": victim.communication_status.value,
                    "preferred_contact": victim.preferred_contact_method
                },
                "investigation_details": {
                    "target_addresses_count": len(case.target_addresses),
                    "investigation_depth": case.investigation_depth,
                    "investigation_scope": case.investigation_scope,
                    "evidence_items_count": len(case.evidence_items),
                    "milestones_completed": case.milestones_completed,
                    "next_actions": case.next_actions
                },
                "communication_stats": {
                    "total_communications": len(case_comms),
                    "outbound_count": len([r for r in case_comms if r.direction == "outbound"]),
                    "inbound_count": len([r for r in case_comms if r.direction == "inbound"]),
                    "last_communication": case_comms[0].timestamp.isoformat() if case_comms else None
                },
                "folder_structure": {
                    "case_folder": case.case_folder_path,
                    "evidence_folder": str(Path(case.case_folder_path) / "evidence"),
                    "reports_folder": str(Path(case.case_folder_path) / "reports"),
                    "communication_folder": str(Path(case.case_folder_path) / "communication")
                }
            }

        except Exception as e:
            logger.error(f"Error generating case summary: {e}")
            return {"error": f"Failed to generate case summary: {e}"}
