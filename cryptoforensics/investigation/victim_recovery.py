"""
Victim fund recovery tools for CryptoForensics

Provides specialized tools for tracking stolen funds, coordinating with exchanges,
and assisting in victim fund recovery operations for law enforcement.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

from ..models import TransactionInfo
from ..core.config import InvestigationConfig
from ..exceptions import InvestigationError
from ..utils.performance import performance_monitor
from ..utils.time_utils import TimeUtils

logger = logging.getLogger(__name__)

class RecoveryStatus(Enum):
    """Recovery case status."""
    INITIATED = "initiated"
    INVESTIGATING = "investigating"
    FUNDS_LOCATED = "funds_located"
    EXCHANGE_CONTACTED = "exchange_contacted"
    FREEZE_REQUESTED = "freeze_requested"
    FUNDS_FROZEN = "funds_frozen"
    RECOVERY_IN_PROGRESS = "recovery_in_progress"
    FUNDS_RECOVERED = "funds_recovered"
    CASE_CLOSED = "case_closed"

class AlertPriority(Enum):
    """Alert priority levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class VictimCase:
    """Victim fund recovery case."""
    case_id: str
    victim_id: str
    incident_date: datetime
    stolen_amount: float
    currency: str
    theft_addresses: List[str]
    victim_addresses: List[str]
    status: RecoveryStatus
    assigned_investigator: str
    priority: AlertPriority
    description: str
    evidence_items: List[str]
    recovery_progress: Dict[str, Any]
    created_at: datetime
    updated_at: datetime

@dataclass
class FundMovementAlert:
    """Alert for fund movement detection."""
    alert_id: str
    case_id: str
    alert_type: str
    priority: AlertPriority
    description: str
    affected_addresses: List[str]
    transaction_ids: List[str]
    amount_moved: float
    destination_info: Dict[str, Any]
    timestamp: datetime
    acknowledged: bool = False

@dataclass
class ExchangeContact:
    """Exchange contact information."""
    exchange_id: str
    exchange_name: str
    contact_type: str  # "law_enforcement", "compliance", "security"
    contact_email: str
    contact_phone: Optional[str]
    jurisdiction: str
    response_time_hours: int
    cooperation_level: str  # "high", "medium", "low"
    special_procedures: List[str]

@dataclass
class RecoveryAction:
    """Recovery action taken."""
    action_id: str
    case_id: str
    action_type: str
    description: str
    taken_by: str
    timestamp: datetime
    status: str
    result: Optional[str]
    evidence_collected: List[str]

class VictimRecoveryManager:
    """
    Comprehensive victim fund recovery management system.
    
    Provides specialized tools for:
    - Tracking stolen cryptocurrency funds
    - Automated exchange monitoring and alerting
    - Coordination with law enforcement
    - Recovery operation management
    - Victim communication and updates
    """
    
    def __init__(self, config: InvestigationConfig):
        """
        Initialize victim recovery manager.
        
        Args:
            config: Investigation configuration
        """
        self.config = config
        self.active_cases = {}
        self.exchange_contacts = {}
        self.monitoring_addresses = set()
        self.active_alerts = {}
        self.recovery_actions = []
        
        # Initialize exchange database
        self._initialize_exchange_contacts()
        
        logger.info("Victim recovery manager initialized")
    
    def _initialize_exchange_contacts(self) -> None:
        """Initialize exchange contact database."""
        self.exchange_contacts = {
            "binance": ExchangeContact(
                exchange_id="binance",
                exchange_name="Binance",
                contact_type="law_enforcement",
                contact_email="<EMAIL>",
                contact_phone="******-0123",
                jurisdiction="global",
                response_time_hours=24,
                cooperation_level="high",
                special_procedures=["court_order_required", "kyc_verification"]
            ),
            "coinbase": ExchangeContact(
                exchange_id="coinbase",
                exchange_name="Coinbase",
                contact_type="law_enforcement",
                contact_email="<EMAIL>",
                contact_phone="******-0124",
                jurisdiction="us",
                response_time_hours=48,
                cooperation_level="high",
                special_procedures=["subpoena_required", "us_jurisdiction_only"]
            ),
            "kraken": ExchangeContact(
                exchange_id="kraken",
                exchange_name="Kraken",
                contact_type="compliance",
                contact_email="<EMAIL>",
                jurisdiction="us_eu",
                response_time_hours=72,
                cooperation_level="medium",
                special_procedures=["formal_request_required"]
            ),
            "bitfinex": ExchangeContact(
                exchange_id="bitfinex",
                exchange_name="Bitfinex",
                contact_type="security",
                contact_email="<EMAIL>",
                jurisdiction="global",
                response_time_hours=96,
                cooperation_level="medium",
                special_procedures=["security_incident_only"]
            )
        }
    
    @performance_monitor("case_creation")
    async def create_recovery_case_async(self, victim_info: Dict[str, Any], 
                                       incident_details: Dict[str, Any]) -> VictimCase:
        """
        Create new victim fund recovery case.
        
        Args:
            victim_info: Victim information
            incident_details: Incident details
            
        Returns:
            Created VictimCase
        """
        try:
            case_id = f"VRC_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Parse incident date
            incident_date = TimeUtils.parse_timestamp(incident_details.get("incident_date", ""))
            if not incident_date:
                incident_date = datetime.now()
            
            # Determine priority based on amount and urgency
            stolen_amount = float(incident_details.get("stolen_amount", 0))
            priority = self._calculate_case_priority(stolen_amount, incident_date)
            
            case = VictimCase(
                case_id=case_id,
                victim_id=victim_info.get("victim_id", f"victim_{int(datetime.now().timestamp())}"),
                incident_date=incident_date,
                stolen_amount=stolen_amount,
                currency=incident_details.get("currency", "BTC"),
                theft_addresses=incident_details.get("theft_addresses", []),
                victim_addresses=victim_info.get("victim_addresses", []),
                status=RecoveryStatus.INITIATED,
                assigned_investigator=incident_details.get("investigator", "unassigned"),
                priority=priority,
                description=incident_details.get("description", ""),
                evidence_items=[],
                recovery_progress={
                    "funds_traced": 0.0,
                    "exchanges_contacted": 0,
                    "freeze_requests_sent": 0,
                    "funds_frozen": 0.0,
                    "funds_recovered": 0.0
                },
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            # Store case
            self.active_cases[case_id] = case
            
            # Start monitoring theft addresses
            for address in case.theft_addresses:
                self.monitoring_addresses.add(address)
            
            # Log case creation
            await self._log_recovery_action(
                case_id, "case_created", f"Recovery case created for {stolen_amount} {case.currency}",
                case.assigned_investigator
            )
            
            logger.info(f"Recovery case created: {case_id} for {stolen_amount} {case.currency}")
            return case
            
        except Exception as e:
            logger.error(f"Failed to create recovery case: {e}")
            raise InvestigationError(f"Case creation failed: {e}")
    
    def _calculate_case_priority(self, stolen_amount: float, incident_date: datetime) -> AlertPriority:
        """Calculate case priority based on amount and recency."""
        # Time factor (more recent = higher priority)
        days_since_incident = (datetime.now() - incident_date).days
        time_factor = max(0, 1 - (days_since_incident / 30))  # Decreases over 30 days
        
        # Amount factor (higher amount = higher priority)
        if stolen_amount >= 100:  # 100+ BTC
            amount_factor = 1.0
        elif stolen_amount >= 10:  # 10+ BTC
            amount_factor = 0.8
        elif stolen_amount >= 1:   # 1+ BTC
            amount_factor = 0.6
        else:
            amount_factor = 0.4
        
        # Combined priority score
        priority_score = (time_factor + amount_factor) / 2
        
        if priority_score >= 0.8:
            return AlertPriority.CRITICAL
        elif priority_score >= 0.6:
            return AlertPriority.HIGH
        elif priority_score >= 0.4:
            return AlertPriority.MEDIUM
        else:
            return AlertPriority.LOW
    
    @performance_monitor("fund_monitoring")
    async def monitor_fund_movement_async(self, transactions: List[TransactionInfo]) -> List[FundMovementAlert]:
        """
        Monitor transactions for movement of tracked funds.
        
        Args:
            transactions: List of transactions to analyze
            
        Returns:
            List of fund movement alerts
        """
        try:
            alerts = []
            
            for tx in transactions:
                # Check if transaction involves monitored addresses
                if tx.from_address in self.monitoring_addresses or tx.to_address in self.monitoring_addresses:
                    alert = await self._create_movement_alert(tx)
                    if alert:
                        alerts.append(alert)
            
            # Process alerts for exchange detection
            for alert in alerts:
                await self._check_exchange_involvement(alert)
            
            logger.info(f"Generated {len(alerts)} fund movement alerts")
            return alerts
            
        except Exception as e:
            logger.error(f"Fund monitoring failed: {e}")
            raise InvestigationError(f"Fund monitoring failed: {e}")
    
    async def _create_movement_alert(self, transaction: TransactionInfo) -> Optional[FundMovementAlert]:
        """Create fund movement alert for transaction."""
        # Find relevant case
        relevant_case = None
        for case in self.active_cases.values():
            if (transaction.from_address in case.theft_addresses or 
                transaction.to_address in case.theft_addresses):
                relevant_case = case
                break
        
        if not relevant_case:
            return None
        
        # Determine alert type and priority
        alert_type = "fund_movement"
        priority = AlertPriority.MEDIUM
        
        # Check for high-risk patterns
        if transaction.amount_btc >= 10:
            alert_type = "large_movement"
            priority = AlertPriority.HIGH
        
        # Check for exchange involvement
        destination_info = await self._analyze_destination(transaction.to_address)
        if destination_info.get("is_exchange", False):
            alert_type = "exchange_deposit"
            priority = AlertPriority.CRITICAL
        
        alert_id = f"alert_{transaction.txid[:16]}"
        
        alert = FundMovementAlert(
            alert_id=alert_id,
            case_id=relevant_case.case_id,
            alert_type=alert_type,
            priority=priority,
            description=f"Funds moved: {transaction.amount_btc} BTC to {transaction.to_address[:16]}...",
            affected_addresses=[transaction.from_address, transaction.to_address],
            transaction_ids=[transaction.txid],
            amount_moved=transaction.amount_btc,
            destination_info=destination_info,
            timestamp=TimeUtils.parse_timestamp(transaction.timestamp) or datetime.now()
        )
        
        self.active_alerts[alert_id] = alert
        return alert
    
    async def _analyze_destination(self, address: str) -> Dict[str, Any]:
        """Analyze destination address for exchange or service identification."""
        # This would integrate with exchange address databases
        # For now, return simulated analysis
        
        # Simulate exchange detection based on address patterns
        known_exchange_patterns = {
            "1": "possible_exchange",  # Addresses starting with 1
            "3": "possible_exchange",  # Addresses starting with 3
            "bc1": "possible_segwit"   # Bech32 addresses
        }
        
        address_type = "unknown"
        is_exchange = False
        exchange_name = None
        
        for pattern, type_name in known_exchange_patterns.items():
            if address.startswith(pattern):
                address_type = type_name
                # Simulate exchange identification (would use real database)
                if len(address) > 30 and pattern in ["1", "3"]:
                    is_exchange = True
                    exchange_name = "unknown_exchange"
                break
        
        return {
            "address": address,
            "address_type": address_type,
            "is_exchange": is_exchange,
            "exchange_name": exchange_name,
            "confidence": 0.7 if is_exchange else 0.3,
            "analysis_timestamp": datetime.now().isoformat()
        }
    
    async def _check_exchange_involvement(self, alert: FundMovementAlert) -> None:
        """Check if alert involves known exchanges and take action."""
        if alert.destination_info.get("is_exchange", False):
            exchange_name = alert.destination_info.get("exchange_name")
            
            if exchange_name and exchange_name in self.exchange_contacts:
                # Automatically initiate exchange contact procedure
                await self._initiate_exchange_contact(alert, exchange_name)
    
    async def _initiate_exchange_contact(self, alert: FundMovementAlert, exchange_name: str) -> None:
        """Initiate contact with exchange for fund freeze."""
        exchange_contact = self.exchange_contacts[exchange_name]
        case = self.active_cases.get(alert.case_id)
        
        if not case:
            return
        
        # Log exchange contact action
        await self._log_recovery_action(
            alert.case_id,
            "exchange_contact_initiated",
            f"Initiated contact with {exchange_contact.exchange_name} for fund freeze",
            case.assigned_investigator
        )
        
        # Update case status
        if case.status == RecoveryStatus.FUNDS_LOCATED:
            case.status = RecoveryStatus.EXCHANGE_CONTACTED
            case.updated_at = datetime.now()
        
        # Update recovery progress
        case.recovery_progress["exchanges_contacted"] += 1
        
        logger.info(f"Exchange contact initiated: {exchange_name} for case {alert.case_id}")
    
    async def _log_recovery_action(self, case_id: str, action_type: str, 
                                 description: str, taken_by: str) -> None:
        """Log recovery action."""
        action = RecoveryAction(
            action_id=f"action_{len(self.recovery_actions) + 1}",
            case_id=case_id,
            action_type=action_type,
            description=description,
            taken_by=taken_by,
            timestamp=datetime.now(),
            status="completed",
            result=None,
            evidence_collected=[]
        )
        
        self.recovery_actions.append(action)
    
    @performance_monitor("exchange_coordination")
    async def coordinate_with_exchange_async(self, case_id: str, exchange_name: str, 
                                           request_type: str) -> Dict[str, Any]:
        """
        Coordinate with exchange for fund recovery.
        
        Args:
            case_id: Recovery case ID
            exchange_name: Exchange identifier
            request_type: Type of request (freeze, information, recovery)
            
        Returns:
            Coordination result
        """
        try:
            case = self.active_cases.get(case_id)
            if not case:
                raise InvestigationError(f"Case not found: {case_id}")
            
            exchange_contact = self.exchange_contacts.get(exchange_name)
            if not exchange_contact:
                raise InvestigationError(f"Exchange contact not found: {exchange_name}")
            
            # Prepare request package
            request_package = {
                "case_id": case_id,
                "request_type": request_type,
                "exchange": exchange_name,
                "victim_info": {
                    "victim_id": case.victim_id,
                    "incident_date": case.incident_date.isoformat(),
                    "stolen_amount": case.stolen_amount,
                    "currency": case.currency
                },
                "addresses_of_interest": case.theft_addresses,
                "legal_authority": case.assigned_investigator,
                "urgency": case.priority.value,
                "contact_info": {
                    "email": exchange_contact.contact_email,
                    "phone": exchange_contact.contact_phone,
                    "expected_response_time": exchange_contact.response_time_hours
                },
                "required_procedures": exchange_contact.special_procedures,
                "timestamp": datetime.now().isoformat()
            }
            
            # Log coordination action
            await self._log_recovery_action(
                case_id,
                f"exchange_{request_type}_request",
                f"Sent {request_type} request to {exchange_contact.exchange_name}",
                case.assigned_investigator
            )
            
            # Update case status based on request type
            if request_type == "freeze":
                case.status = RecoveryStatus.FREEZE_REQUESTED
                case.recovery_progress["freeze_requests_sent"] += 1
            
            case.updated_at = datetime.now()
            
            logger.info(f"Exchange coordination initiated: {request_type} request to {exchange_name}")
            return request_package
            
        except Exception as e:
            logger.error(f"Exchange coordination failed: {e}")
            raise InvestigationError(f"Exchange coordination failed: {e}")
    
    async def update_case_status_async(self, case_id: str, new_status: RecoveryStatus, 
                                     notes: Optional[str] = None) -> None:
        """Update case status with optional notes."""
        case = self.active_cases.get(case_id)
        if not case:
            raise InvestigationError(f"Case not found: {case_id}")
        
        old_status = case.status
        case.status = new_status
        case.updated_at = datetime.now()
        
        # Log status change
        description = f"Status changed from {old_status.value} to {new_status.value}"
        if notes:
            description += f". Notes: {notes}"
        
        await self._log_recovery_action(
            case_id, "status_update", description, case.assigned_investigator
        )
        
        logger.info(f"Case {case_id} status updated: {old_status.value} -> {new_status.value}")
    
    def get_case_summary(self, case_id: str) -> Dict[str, Any]:
        """Get comprehensive case summary."""
        case = self.active_cases.get(case_id)
        if not case:
            return {"error": "Case not found"}
        
        # Get related alerts
        case_alerts = [alert for alert in self.active_alerts.values() 
                      if alert.case_id == case_id]
        
        # Get related actions
        case_actions = [action for action in self.recovery_actions 
                       if action.case_id == case_id]
        
        return {
            "case_info": {
                "case_id": case.case_id,
                "victim_id": case.victim_id,
                "status": case.status.value,
                "priority": case.priority.value,
                "stolen_amount": case.stolen_amount,
                "currency": case.currency,
                "incident_date": case.incident_date.isoformat(),
                "assigned_investigator": case.assigned_investigator
            },
            "progress": case.recovery_progress,
            "alerts": len(case_alerts),
            "actions_taken": len(case_actions),
            "monitoring_addresses": len(case.theft_addresses),
            "last_updated": case.updated_at.isoformat()
        }
    
    def get_active_alerts(self, priority_filter: Optional[AlertPriority] = None) -> List[Dict[str, Any]]:
        """Get active alerts with optional priority filter."""
        alerts = []
        
        for alert in self.active_alerts.values():
            if not alert.acknowledged and (not priority_filter or alert.priority == priority_filter):
                alerts.append({
                    "alert_id": alert.alert_id,
                    "case_id": alert.case_id,
                    "alert_type": alert.alert_type,
                    "priority": alert.priority.value,
                    "description": alert.description,
                    "amount_moved": alert.amount_moved,
                    "timestamp": alert.timestamp.isoformat()
                })
        
        # Sort by priority and timestamp
        priority_order = {AlertPriority.CRITICAL: 4, AlertPriority.HIGH: 3, 
                         AlertPriority.MEDIUM: 2, AlertPriority.LOW: 1}
        
        alerts.sort(key=lambda x: (priority_order.get(AlertPriority(x["priority"]), 0), x["timestamp"]), 
                   reverse=True)
        
        return alerts
    
    def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge an alert."""
        alert = self.active_alerts.get(alert_id)
        if alert:
            alert.acknowledged = True
            logger.info(f"Alert acknowledged: {alert_id}")
            return True
        return False
