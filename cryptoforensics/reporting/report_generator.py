"""
Professional report generation for CryptoForensics

Provides comprehensive report generation with legal templates,
expert witness documentation, and professional formatting.
"""

import logging
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime

from ..core.config import InvestigationConfig
from ..models import TransactionInfo

logger = logging.getLogger(__name__)

class ReportGenerator:
    """
    Professional report generation system.

    Creates comprehensive investigation reports with legal compliance,
    expert witness documentation, and professional formatting.
    """

    def __init__(self, config: InvestigationConfig, investigation_id: str):
        """Initialize report generator."""
        self.config = config
        self.investigation_id = investigation_id

        logger.info("Report generator initialized")

    def generate_comprehensive_report(self,
                                    transactions: List[TransactionInfo],
                                    analysis_results: Dict[str, Any],
                                    save_to_file: bool = True) -> Optional[str]:
        """
        Generate comprehensive investigation report.

        Args:
            transactions: List of transactions
            analysis_results: Analysis results
            save_to_file: Whether to save to file

        Returns:
            Path to saved report or None
        """
        try:
            report_content = self._create_report_content(transactions, analysis_results)

            if save_to_file and self.config.save_reports:
                output_path = Path(self.config.output_directory)
                filename = output_path / f"investigation_report_{self.investigation_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(report_content)

                logger.info(f"Report saved to {filename}")
                return str(filename)

            return None

        except Exception as e:
            logger.error(f"Error generating report: {e}")
            return None

    def _create_report_content(self,
                              transactions: List[TransactionInfo],
                              analysis_results: Dict[str, Any]) -> str:
        """Create comprehensive report content."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("                    CRYPTOFORENSICS INVESTIGATION REPORT")
        report_lines.append(f"                        Generated: {timestamp}")
        report_lines.append(f"                     Investigation ID: {self.investigation_id}")
        report_lines.append("=" * 80)
        report_lines.append("")

        if not transactions:
            report_lines.append("No transactions were traced from the initial parameters.")
            report_lines.append("")
        else:
            # Summary statistics
            total_amount = sum(tx.amount_btc for tx in transactions)
            unique_addresses = len(set(tx.to_address for tx in transactions))

            report_lines.append("📊 INVESTIGATION SUMMARY")
            report_lines.append("-" * 40)
            report_lines.append(f"Total transactions traced: {len(transactions)}")
            report_lines.append(f"Total amount moved: {total_amount:.8f} BTC")
            report_lines.append(f"Unique destination addresses: {unique_addresses}")
            report_lines.append("")

            # Risk assessment
            if "risk_assessment" in analysis_results:
                risk_data = analysis_results["risk_assessment"]
                report_lines.append("⚠️  RISK ASSESSMENT")
                report_lines.append("-" * 40)
                report_lines.append(f"Risk Level: {risk_data.get('risk_level', 'UNKNOWN')}")
                report_lines.append(f"Composite Score: {risk_data.get('composite_risk_score', 0):.2f}")
                report_lines.append("")

            # Suspicious activities
            if "suspicious_activity" in analysis_results:
                suspicious_data = analysis_results["suspicious_activity"]
                report_lines.append("🚨 SUSPICIOUS ACTIVITIES")
                report_lines.append("-" * 40)

                detected_activities = [
                    activity for activity, details in suspicious_data.get("activities", {}).items()
                    if isinstance(details, dict) and details.get("detected", False)
                ]

                if detected_activities:
                    for activity in detected_activities:
                        report_lines.append(f"• {activity.replace('_', ' ').title()}")
                else:
                    report_lines.append("No suspicious activities detected")
                report_lines.append("")

            # Detailed transaction list
            report_lines.append("📋 DETAILED TRANSACTION TRAIL")
            report_lines.append("-" * 40)

            for i, tx in enumerate(transactions[:20], 1):  # Limit to first 20
                report_lines.append(f"Transaction #{i} (Depth: {tx.depth})")
                report_lines.append(f"   ➡️  From: {tx.from_address}")
                report_lines.append(f"   🎯  To:   {tx.to_address}")
                report_lines.append(f"   💰  Amount: {tx.amount_btc:.8f} BTC")
                report_lines.append(f"   🔗  TXID: {tx.txid}")
                report_lines.append(f"   🕐  Timestamp: {tx.timestamp}")
                report_lines.append("")

            if len(transactions) > 20:
                report_lines.append(f"... and {len(transactions) - 20} more transactions")
                report_lines.append("")

        report_lines.append("=" * 80)
        report_lines.append("                           END OF REPORT")
        report_lines.append("=" * 80)

        return '\n'.join(report_lines)
