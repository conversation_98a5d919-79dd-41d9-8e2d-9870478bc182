"""
Enhanced security measures for CryptoForensics

Provides comprehensive security features including end-to-end encryption,
secure key management, access control, and security monitoring.
"""

import asyncio
import logging
import secrets
import hashlib
import hmac
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.kdf.scrypt import Scrypt
import jwt
import bcrypt

from ..core.config import InvestigationConfig
from ..exceptions import SecurityError
from ..utils.crypto import CryptoUtils, SecureRandom
from ..utils.performance import performance_monitor

logger = logging.getLogger(__name__)

@dataclass
class SecurityContext:
    """Security context for operations."""
    user_id: str
    session_id: str
    permissions: List[str]
    access_level: str
    created_at: datetime
    expires_at: datetime
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None

@dataclass
class AccessAttempt:
    """Access attempt record."""
    attempt_id: str
    user_id: str
    resource: str
    action: str
    success: bool
    timestamp: datetime
    ip_address: Optional[str] = None
    failure_reason: Optional[str] = None

@dataclass
class SecurityAlert:
    """Security alert."""
    alert_id: str
    alert_type: str
    severity: str
    description: str
    user_id: Optional[str]
    ip_address: Optional[str]
    timestamp: datetime
    evidence: Dict[str, Any]

class EnhancedSecurity:
    """
    Enhanced security system for CryptoForensics.
    
    Provides comprehensive security features including:
    - End-to-end encryption for all sensitive data
    - Secure key management and storage
    - Role-based access control
    - Security monitoring and alerting
    - Input sanitization and validation
    - Session management
    """
    
    def __init__(self, config: InvestigationConfig):
        """
        Initialize enhanced security system.
        
        Args:
            config: Investigation configuration
        """
        self.config = config
        self.active_sessions = {}
        self.access_attempts = []
        self.security_alerts = []
        self.failed_attempts = {}
        self.encryption_keys = {}
        
        # Initialize security components
        self._initialize_security_keys()
        self._initialize_access_control()
        
        logger.info("Enhanced security system initialized")
    
    def _initialize_security_keys(self) -> None:
        """Initialize encryption keys and security materials."""
        # Generate master encryption key
        self.master_key = SecureRandom.generate_bytes(32)
        
        # Generate RSA key pair for digital signatures
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=4096,
        )
        
        self.private_key = private_key
        self.public_key = private_key.public_key()
        
        # Generate JWT signing key
        self.jwt_secret = SecureRandom.generate_bytes(64)
        
        logger.info("Security keys initialized")
    
    def _initialize_access_control(self) -> None:
        """Initialize access control system."""
        self.permissions = {
            "admin": [
                "read_all", "write_all", "delete_all", "manage_users",
                "view_audit_logs", "manage_security", "export_data"
            ],
            "investigator": [
                "read_investigations", "write_investigations", "analyze_data",
                "view_evidence", "create_reports"
            ],
            "analyst": [
                "read_investigations", "analyze_data", "view_evidence",
                "create_basic_reports"
            ],
            "viewer": [
                "read_investigations", "view_evidence"
            ]
        }
        
        self.access_levels = ["public", "internal", "confidential", "restricted", "top_secret"]
        
        logger.info("Access control system initialized")
    
    @performance_monitor("user_authentication")
    async def authenticate_user_async(self, username: str, password: str, 
                                    ip_address: Optional[str] = None) -> Optional[SecurityContext]:
        """
        Authenticate user with enhanced security measures.
        
        Args:
            username: Username
            password: Password
            ip_address: Client IP address
            
        Returns:
            SecurityContext if authentication successful, None otherwise
        """
        try:
            # Check for rate limiting
            if self._is_rate_limited(username, ip_address):
                await self._log_security_alert("rate_limit_exceeded", username, ip_address)
                return None
            
            # Simulate user lookup (in real implementation, this would query a database)
            user_data = await self._lookup_user(username)
            if not user_data:
                await self._record_failed_attempt(username, "user_not_found", ip_address)
                return None
            
            # Verify password
            if not self._verify_password(password, user_data["password_hash"]):
                await self._record_failed_attempt(username, "invalid_password", ip_address)
                return None
            
            # Check if account is locked
            if user_data.get("locked", False):
                await self._log_security_alert("locked_account_access", username, ip_address)
                return None
            
            # Create security context
            session_id = SecureRandom.generate_token(32)
            context = SecurityContext(
                user_id=user_data["user_id"],
                session_id=session_id,
                permissions=self.permissions.get(user_data["role"], []),
                access_level=user_data.get("access_level", "internal"),
                created_at=datetime.now(),
                expires_at=datetime.now() + timedelta(hours=8),
                ip_address=ip_address
            )
            
            # Store active session
            self.active_sessions[session_id] = context
            
            # Log successful authentication
            await self._record_access_attempt(
                user_data["user_id"], "authentication", "login", True, ip_address
            )
            
            logger.info(f"User {username} authenticated successfully")
            return context
            
        except Exception as e:
            logger.error(f"Authentication failed for {username}: {e}")
            await self._log_security_alert("authentication_error", username, ip_address, {"error": str(e)})
            return None
    
    async def _lookup_user(self, username: str) -> Optional[Dict[str, Any]]:
        """Lookup user data (simulated)."""
        # In real implementation, this would query a secure database
        users = {
            "admin": {
                "user_id": "admin_001",
                "username": "admin",
                "password_hash": bcrypt.hashpw("admin123".encode(), bcrypt.gensalt()),
                "role": "admin",
                "access_level": "top_secret",
                "locked": False
            },
            "investigator": {
                "user_id": "inv_001", 
                "username": "investigator",
                "password_hash": bcrypt.hashpw("inv123".encode(), bcrypt.gensalt()),
                "role": "investigator",
                "access_level": "restricted",
                "locked": False
            }
        }
        return users.get(username)
    
    def _verify_password(self, password: str, password_hash: bytes) -> bool:
        """Verify password against hash."""
        try:
            return bcrypt.checkpw(password.encode(), password_hash)
        except Exception:
            return False
    
    def _is_rate_limited(self, username: str, ip_address: Optional[str]) -> bool:
        """Check if user/IP is rate limited."""
        current_time = time.time()
        
        # Check failed attempts for this user
        user_attempts = self.failed_attempts.get(username, [])
        recent_attempts = [t for t in user_attempts if current_time - t < 300]  # 5 minutes
        
        if len(recent_attempts) >= 5:  # Max 5 failed attempts in 5 minutes
            return True
        
        # Check failed attempts for this IP
        if ip_address:
            ip_attempts = self.failed_attempts.get(f"ip_{ip_address}", [])
            recent_ip_attempts = [t for t in ip_attempts if current_time - t < 300]
            
            if len(recent_ip_attempts) >= 10:  # Max 10 failed attempts per IP in 5 minutes
                return True
        
        return False
    
    async def _record_failed_attempt(self, username: str, reason: str, ip_address: Optional[str]) -> None:
        """Record failed authentication attempt."""
        current_time = time.time()
        
        # Record for username
        if username not in self.failed_attempts:
            self.failed_attempts[username] = []
        self.failed_attempts[username].append(current_time)
        
        # Record for IP address
        if ip_address:
            ip_key = f"ip_{ip_address}"
            if ip_key not in self.failed_attempts:
                self.failed_attempts[ip_key] = []
            self.failed_attempts[ip_key].append(current_time)
        
        # Log access attempt
        await self._record_access_attempt(username, "authentication", "login", False, ip_address, reason)
        
        logger.warning(f"Failed authentication attempt for {username}: {reason}")
    
    async def _record_access_attempt(self, user_id: str, resource: str, action: str, 
                                   success: bool, ip_address: Optional[str] = None,
                                   failure_reason: Optional[str] = None) -> None:
        """Record access attempt."""
        attempt = AccessAttempt(
            attempt_id=SecureRandom.generate_token(16),
            user_id=user_id,
            resource=resource,
            action=action,
            success=success,
            timestamp=datetime.now(),
            ip_address=ip_address,
            failure_reason=failure_reason
        )
        
        self.access_attempts.append(attempt)
        
        # Keep only recent attempts (last 1000)
        if len(self.access_attempts) > 1000:
            self.access_attempts = self.access_attempts[-1000:]
    
    async def _log_security_alert(self, alert_type: str, user_id: Optional[str] = None,
                                ip_address: Optional[str] = None, evidence: Optional[Dict[str, Any]] = None) -> None:
        """Log security alert."""
        alert = SecurityAlert(
            alert_id=SecureRandom.generate_token(16),
            alert_type=alert_type,
            severity=self._get_alert_severity(alert_type),
            description=self._get_alert_description(alert_type),
            user_id=user_id,
            ip_address=ip_address,
            timestamp=datetime.now(),
            evidence=evidence or {}
        )
        
        self.security_alerts.append(alert)
        
        # Keep only recent alerts (last 500)
        if len(self.security_alerts) > 500:
            self.security_alerts = self.security_alerts[-500:]
        
        logger.warning(f"Security alert: {alert_type} - {alert.description}")
    
    def _get_alert_severity(self, alert_type: str) -> str:
        """Get severity level for alert type."""
        severity_map = {
            "rate_limit_exceeded": "medium",
            "locked_account_access": "high",
            "authentication_error": "medium",
            "unauthorized_access": "high",
            "privilege_escalation": "critical",
            "data_breach_attempt": "critical",
            "suspicious_activity": "medium"
        }
        return severity_map.get(alert_type, "low")
    
    def _get_alert_description(self, alert_type: str) -> str:
        """Get description for alert type."""
        descriptions = {
            "rate_limit_exceeded": "Rate limit exceeded for authentication attempts",
            "locked_account_access": "Attempt to access locked account",
            "authentication_error": "Authentication system error",
            "unauthorized_access": "Unauthorized access attempt",
            "privilege_escalation": "Privilege escalation attempt detected",
            "data_breach_attempt": "Potential data breach attempt",
            "suspicious_activity": "Suspicious user activity detected"
        }
        return descriptions.get(alert_type, "Unknown security event")
    
    @performance_monitor("authorization_check")
    async def check_authorization_async(self, session_id: str, resource: str, 
                                      action: str, data_classification: str = "internal") -> bool:
        """
        Check if user is authorized for specific action.
        
        Args:
            session_id: User session ID
            resource: Resource being accessed
            action: Action being performed
            data_classification: Data classification level
            
        Returns:
            True if authorized, False otherwise
        """
        try:
            # Get security context
            context = self.active_sessions.get(session_id)
            if not context:
                await self._log_security_alert("unauthorized_access", None, None, 
                                              {"session_id": session_id, "resource": resource})
                return False
            
            # Check session expiry
            if datetime.now() > context.expires_at:
                await self._invalidate_session(session_id)
                return False
            
            # Check permissions
            required_permission = f"{action}_{resource}"
            if required_permission not in context.permissions and "admin" not in context.permissions:
                await self._log_security_alert("unauthorized_access", context.user_id, context.ip_address,
                                              {"resource": resource, "action": action})
                return False
            
            # Check data classification access
            if not self._check_data_classification_access(context.access_level, data_classification):
                await self._log_security_alert("unauthorized_access", context.user_id, context.ip_address,
                                              {"data_classification": data_classification})
                return False
            
            # Log successful access
            await self._record_access_attempt(context.user_id, resource, action, True, context.ip_address)
            
            return True
            
        except Exception as e:
            logger.error(f"Authorization check failed: {e}")
            return False
    
    def _check_data_classification_access(self, user_access_level: str, data_classification: str) -> bool:
        """Check if user access level allows access to data classification."""
        user_level_index = self.access_levels.index(user_access_level)
        data_level_index = self.access_levels.index(data_classification)
        
        return user_level_index >= data_level_index
    
    async def _invalidate_session(self, session_id: str) -> None:
        """Invalidate user session."""
        if session_id in self.active_sessions:
            context = self.active_sessions[session_id]
            await self._record_access_attempt(context.user_id, "session", "logout", True, context.ip_address)
            del self.active_sessions[session_id]
            logger.info(f"Session {session_id} invalidated")
    
    @performance_monitor("data_encryption")
    async def encrypt_sensitive_data_async(self, data: Dict[str, Any], 
                                         classification: str = "confidential") -> Dict[str, Any]:
        """
        Encrypt sensitive data with appropriate encryption strength.
        
        Args:
            data: Data to encrypt
            classification: Data classification level
            
        Returns:
            Encrypted data package
        """
        try:
            # Determine encryption parameters based on classification
            encryption_params = self._get_encryption_parameters(classification)
            
            # Generate encryption key
            key = self._derive_encryption_key(classification)
            
            # Encrypt data
            encrypted_data = CryptoUtils.encrypt_data(data, key)
            
            # Create integrity proof
            integrity_proof = CryptoUtils.create_integrity_proof(data, self.private_key)
            
            # Package encrypted data
            package = {
                "encrypted_data": encrypted_data.hex(),
                "classification": classification,
                "encryption_algorithm": encryption_params["algorithm"],
                "key_derivation": encryption_params["key_derivation"],
                "integrity_proof": integrity_proof,
                "encrypted_at": datetime.now().isoformat(),
                "version": "1.0"
            }
            
            logger.debug(f"Data encrypted with {classification} classification")
            return package
            
        except Exception as e:
            logger.error(f"Data encryption failed: {e}")
            raise SecurityError(f"Encryption failed: {e}")
    
    def _get_encryption_parameters(self, classification: str) -> Dict[str, str]:
        """Get encryption parameters based on data classification."""
        params = {
            "public": {"algorithm": "AES-128", "key_derivation": "PBKDF2"},
            "internal": {"algorithm": "AES-256", "key_derivation": "PBKDF2"},
            "confidential": {"algorithm": "AES-256", "key_derivation": "Scrypt"},
            "restricted": {"algorithm": "AES-256", "key_derivation": "Scrypt"},
            "top_secret": {"algorithm": "AES-256", "key_derivation": "Scrypt"}
        }
        return params.get(classification, params["internal"])
    
    def _derive_encryption_key(self, classification: str) -> bytes:
        """Derive encryption key based on classification."""
        # Use different key derivation based on classification
        if classification in ["restricted", "top_secret"]:
            # Use Scrypt for high-security data
            kdf = Scrypt(
                algorithm=hashes.SHA256(),
                length=32,
                salt=self.master_key[:16],
                n=2**14,
                r=8,
                p=1,
            )
        else:
            # Use PBKDF2 for standard data
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=self.master_key[:16],
                iterations=100000,
            )
        
        return kdf.derive(self.master_key)
    
    @performance_monitor("input_sanitization")
    def sanitize_input(self, input_data: Any, input_type: str = "general") -> Any:
        """
        Sanitize input data to prevent injection attacks.
        
        Args:
            input_data: Input data to sanitize
            input_type: Type of input (address, txid, amount, etc.)
            
        Returns:
            Sanitized input data
        """
        try:
            if input_data is None:
                return None
            
            if input_type == "address":
                return self._sanitize_address(input_data)
            elif input_type == "txid":
                return self._sanitize_txid(input_data)
            elif input_type == "amount":
                return self._sanitize_amount(input_data)
            elif input_type == "string":
                return self._sanitize_string(input_data)
            else:
                return self._sanitize_general(input_data)
                
        except Exception as e:
            logger.error(f"Input sanitization failed: {e}")
            raise SecurityError(f"Input sanitization failed: {e}")
    
    def _sanitize_address(self, address: str) -> str:
        """Sanitize cryptocurrency address."""
        if not isinstance(address, str):
            raise SecurityError("Address must be a string")
        
        # Remove whitespace
        address = address.strip()
        
        # Check length (Bitcoin addresses are typically 26-35 characters)
        if len(address) < 26 or len(address) > 62:  # Extended for other cryptocurrencies
            raise SecurityError("Invalid address length")
        
        # Check for valid characters (alphanumeric)
        if not address.replace('1', '').replace('3', '').replace('bc1', '').isalnum():
            raise SecurityError("Address contains invalid characters")
        
        return address
    
    def _sanitize_txid(self, txid: str) -> str:
        """Sanitize transaction ID."""
        if not isinstance(txid, str):
            raise SecurityError("Transaction ID must be a string")
        
        # Remove whitespace
        txid = txid.strip()
        
        # Check length (Bitcoin txids are 64 hex characters)
        if len(txid) != 64:
            raise SecurityError("Invalid transaction ID length")
        
        # Check for valid hex characters
        try:
            int(txid, 16)
        except ValueError:
            raise SecurityError("Transaction ID contains invalid characters")
        
        return txid.lower()
    
    def _sanitize_amount(self, amount: Any) -> float:
        """Sanitize amount value."""
        try:
            amount_float = float(amount)
            
            # Check for reasonable bounds
            if amount_float < 0:
                raise SecurityError("Amount cannot be negative")
            
            if amount_float > 21000000:  # Bitcoin max supply
                raise SecurityError("Amount exceeds maximum possible value")
            
            return amount_float
            
        except (ValueError, TypeError):
            raise SecurityError("Invalid amount format")
    
    def _sanitize_string(self, text: str) -> str:
        """Sanitize general string input."""
        if not isinstance(text, str):
            raise SecurityError("Input must be a string")
        
        # Remove potentially dangerous characters
        dangerous_chars = ['<', '>', '"', "'", '&', '\x00', '\n', '\r']
        for char in dangerous_chars:
            text = text.replace(char, '')
        
        # Limit length
        if len(text) > 1000:
            text = text[:1000]
        
        return text.strip()
    
    def _sanitize_general(self, data: Any) -> Any:
        """General sanitization for unknown input types."""
        if isinstance(data, str):
            return self._sanitize_string(data)
        elif isinstance(data, (int, float)):
            return data
        elif isinstance(data, dict):
            return {k: self._sanitize_general(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._sanitize_general(item) for item in data]
        else:
            return data
    
    def get_security_status(self) -> Dict[str, Any]:
        """Get current security status."""
        return {
            "active_sessions": len(self.active_sessions),
            "recent_alerts": len([a for a in self.security_alerts 
                                if (datetime.now() - a.timestamp).total_seconds() < 3600]),
            "failed_attempts_last_hour": len([a for a in self.access_attempts 
                                            if not a.success and (datetime.now() - a.timestamp).total_seconds() < 3600]),
            "security_level": "high",
            "encryption_status": "active",
            "monitoring_status": "active"
        }
