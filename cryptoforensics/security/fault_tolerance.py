"""
Fault tolerance and disaster recovery for CryptoForensics

Provides automatic recovery mechanisms, backup systems, distributed processing
capabilities, and resilience against failures.
"""

import asyncio
import logging
import json
import pickle
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

from ..core.config import InvestigationConfig
from ..exceptions import SystemError
from ..utils.performance import performance_monitor, PerformanceMonitor
from ..utils.crypto import CryptoUtils

logger = logging.getLogger(__name__)

@dataclass
class SystemState:
    """System state snapshot."""
    state_id: str
    timestamp: datetime
    component_states: Dict[str, Any]
    active_operations: List[str]
    memory_usage: float
    cpu_usage: float
    disk_usage: float
    network_status: str

@dataclass
class FailureEvent:
    """System failure event."""
    failure_id: str
    component: str
    failure_type: str
    severity: str
    timestamp: datetime
    error_message: str
    stack_trace: Optional[str]
    recovery_attempted: bool = False
    recovery_successful: bool = False

@dataclass
class RecoveryAction:
    """Recovery action definition."""
    action_id: str
    trigger_conditions: List[str]
    action_type: str
    priority: int
    timeout_seconds: int
    retry_count: int
    recovery_function: Callable

class FaultToleranceManager:
    """
    Comprehensive fault tolerance and disaster recovery system.
    
    Provides:
    - Automatic failure detection and recovery
    - System state monitoring and checkpointing
    - Backup and restore capabilities
    - Distributed processing coordination
    - Circuit breaker patterns
    - Graceful degradation
    """
    
    def __init__(self, config: InvestigationConfig):
        """
        Initialize fault tolerance manager.
        
        Args:
            config: Investigation configuration
        """
        self.config = config
        self.system_states = []
        self.failure_events = []
        self.recovery_actions = {}
        self.circuit_breakers = {}
        self.backup_locations = []
        self.monitoring_active = False
        self.performance_monitor = PerformanceMonitor()
        
        # Initialize components
        self._initialize_recovery_actions()
        self._initialize_circuit_breakers()
        self._setup_backup_system()
        
        logger.info("Fault tolerance manager initialized")
    
    def _initialize_recovery_actions(self) -> None:
        """Initialize automatic recovery actions."""
        self.recovery_actions = {
            "memory_exhaustion": RecoveryAction(
                action_id="memory_recovery",
                trigger_conditions=["memory_usage > 90%"],
                action_type="cleanup",
                priority=1,
                timeout_seconds=30,
                retry_count=3,
                recovery_function=self._recover_memory
            ),
            "api_failure": RecoveryAction(
                action_id="api_recovery",
                trigger_conditions=["api_error_rate > 50%"],
                action_type="restart",
                priority=2,
                timeout_seconds=60,
                retry_count=5,
                recovery_function=self._recover_api_connection
            ),
            "database_failure": RecoveryAction(
                action_id="database_recovery",
                trigger_conditions=["database_connection_failed"],
                action_type="reconnect",
                priority=1,
                timeout_seconds=120,
                retry_count=3,
                recovery_function=self._recover_database_connection
            ),
            "disk_space_low": RecoveryAction(
                action_id="disk_cleanup",
                trigger_conditions=["disk_usage > 85%"],
                action_type="cleanup",
                priority=3,
                timeout_seconds=300,
                retry_count=2,
                recovery_function=self._cleanup_disk_space
            )
        }
    
    def _initialize_circuit_breakers(self) -> None:
        """Initialize circuit breakers for external services."""
        self.circuit_breakers = {
            "blockchain_api": {
                "state": "closed",  # closed, open, half_open
                "failure_count": 0,
                "failure_threshold": 5,
                "timeout": 60,
                "last_failure": None,
                "success_threshold": 3  # for half_open -> closed
            },
            "database": {
                "state": "closed",
                "failure_count": 0,
                "failure_threshold": 3,
                "timeout": 30,
                "last_failure": None,
                "success_threshold": 2
            },
            "external_services": {
                "state": "closed",
                "failure_count": 0,
                "failure_threshold": 10,
                "timeout": 120,
                "last_failure": None,
                "success_threshold": 5
            }
        }
    
    def _setup_backup_system(self) -> None:
        """Setup backup system configuration."""
        self.backup_locations = [
            {
                "type": "local",
                "path": "/var/backups/cryptoforensics",
                "retention_days": 30,
                "enabled": True
            },
            {
                "type": "cloud",
                "provider": "aws_s3",
                "bucket": "cryptoforensics-backups",
                "retention_days": 90,
                "enabled": False  # Would be configured in production
            },
            {
                "type": "remote",
                "host": "backup.example.com",
                "path": "/backups/cryptoforensics",
                "retention_days": 60,
                "enabled": False
            }
        ]
    
    @performance_monitor("system_monitoring")
    async def start_monitoring_async(self) -> None:
        """Start continuous system monitoring."""
        if self.monitoring_active:
            logger.warning("System monitoring already active")
            return
        
        self.monitoring_active = True
        self.performance_monitor.start_monitoring(interval=10.0)
        
        # Start monitoring tasks
        monitoring_tasks = [
            self._monitor_system_health(),
            self._monitor_circuit_breakers(),
            self._periodic_backup(),
            self._cleanup_old_data()
        ]
        
        await asyncio.gather(*monitoring_tasks, return_exceptions=True)
        
        logger.info("System monitoring started")
    
    async def stop_monitoring(self) -> None:
        """Stop system monitoring."""
        self.monitoring_active = False
        self.performance_monitor.stop_monitoring()
        logger.info("System monitoring stopped")
    
    async def _monitor_system_health(self) -> None:
        """Monitor overall system health."""
        while self.monitoring_active:
            try:
                # Capture system state
                state = await self._capture_system_state()
                self.system_states.append(state)
                
                # Keep only recent states (last 100)
                if len(self.system_states) > 100:
                    self.system_states = self.system_states[-100:]
                
                # Check for failure conditions
                await self._check_failure_conditions(state)
                
                # Wait before next check
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"System health monitoring error: {e}")
                await asyncio.sleep(60)  # Longer wait on error
    
    async def _capture_system_state(self) -> SystemState:
        """Capture current system state."""
        memory_stats = self.performance_monitor.get_current_memory_usage()
        
        return SystemState(
            state_id=f"state_{int(time.time())}",
            timestamp=datetime.now(),
            component_states={
                "api_client": "active",
                "database": "connected",
                "evidence_collector": "ready",
                "analysis_engine": "ready"
            },
            active_operations=[],  # Would track actual operations
            memory_usage=memory_stats.get("memory_percent", 0),
            cpu_usage=50.0,  # Simulated - would get actual CPU usage
            disk_usage=30.0,  # Simulated - would get actual disk usage
            network_status="connected"
        )
    
    async def _check_failure_conditions(self, state: SystemState) -> None:
        """Check for failure conditions and trigger recovery."""
        # Check memory usage
        if state.memory_usage > 90:
            await self._handle_failure("memory_exhaustion", "system", "high", 
                                      f"Memory usage at {state.memory_usage}%")
        
        # Check disk usage
        if state.disk_usage > 85:
            await self._handle_failure("disk_space_low", "storage", "medium",
                                      f"Disk usage at {state.disk_usage}%")
        
        # Check component states
        for component, status in state.component_states.items():
            if status in ["failed", "error", "disconnected"]:
                await self._handle_failure(f"{component}_failure", component, "high",
                                          f"Component {component} in {status} state")
    
    async def _handle_failure(self, failure_type: str, component: str, 
                            severity: str, error_message: str) -> None:
        """Handle detected failure."""
        failure_id = f"failure_{int(time.time())}"
        
        failure_event = FailureEvent(
            failure_id=failure_id,
            component=component,
            failure_type=failure_type,
            severity=severity,
            timestamp=datetime.now(),
            error_message=error_message,
            stack_trace=None
        )
        
        self.failure_events.append(failure_event)
        
        logger.error(f"Failure detected: {failure_type} in {component} - {error_message}")
        
        # Attempt automatic recovery
        await self._attempt_recovery(failure_event)
    
    async def _attempt_recovery(self, failure_event: FailureEvent) -> None:
        """Attempt automatic recovery for failure."""
        recovery_action = self.recovery_actions.get(failure_event.failure_type)
        
        if not recovery_action:
            logger.warning(f"No recovery action defined for {failure_event.failure_type}")
            return
        
        failure_event.recovery_attempted = True
        
        try:
            logger.info(f"Attempting recovery: {recovery_action.action_id}")
            
            # Execute recovery function with timeout
            recovery_task = asyncio.create_task(recovery_action.recovery_function())
            
            try:
                await asyncio.wait_for(recovery_task, timeout=recovery_action.timeout_seconds)
                failure_event.recovery_successful = True
                logger.info(f"Recovery successful: {recovery_action.action_id}")
                
            except asyncio.TimeoutError:
                logger.error(f"Recovery timeout: {recovery_action.action_id}")
                recovery_task.cancel()
                
        except Exception as e:
            logger.error(f"Recovery failed: {recovery_action.action_id} - {e}")
            failure_event.recovery_successful = False
    
    async def _recover_memory(self) -> None:
        """Recover from memory exhaustion."""
        logger.info("Executing memory recovery")
        
        # Force garbage collection
        import gc
        collected = gc.collect()
        logger.info(f"Garbage collection freed {collected} objects")
        
        # Clear caches
        # In real implementation, would clear application caches
        logger.info("Cleared application caches")
        
        # Reduce memory usage
        # In real implementation, would implement memory optimization
        await asyncio.sleep(1)  # Simulate recovery time
    
    async def _recover_api_connection(self) -> None:
        """Recover API connection."""
        logger.info("Executing API connection recovery")
        
        # Reset connection pools
        # In real implementation, would reset actual connections
        await asyncio.sleep(2)  # Simulate recovery time
        
        logger.info("API connections reset")
    
    async def _recover_database_connection(self) -> None:
        """Recover database connection."""
        logger.info("Executing database connection recovery")
        
        # Reconnect to database
        # In real implementation, would reconnect to actual database
        await asyncio.sleep(3)  # Simulate recovery time
        
        logger.info("Database connection restored")
    
    async def _cleanup_disk_space(self) -> None:
        """Clean up disk space."""
        logger.info("Executing disk space cleanup")
        
        # Clean temporary files
        # In real implementation, would clean actual temporary files
        
        # Compress old logs
        # In real implementation, would compress actual log files
        
        # Remove old backups
        # In real implementation, would remove old backup files
        
        await asyncio.sleep(5)  # Simulate cleanup time
        logger.info("Disk space cleanup completed")
    
    async def _monitor_circuit_breakers(self) -> None:
        """Monitor and manage circuit breakers."""
        while self.monitoring_active:
            try:
                current_time = time.time()
                
                for service, breaker in self.circuit_breakers.items():
                    if breaker["state"] == "open":
                        # Check if timeout has passed
                        if (breaker["last_failure"] and 
                            current_time - breaker["last_failure"] > breaker["timeout"]):
                            breaker["state"] = "half_open"
                            logger.info(f"Circuit breaker {service} moved to half-open state")
                
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"Circuit breaker monitoring error: {e}")
                await asyncio.sleep(60)
    
    def record_service_failure(self, service: str) -> bool:
        """
        Record service failure and update circuit breaker.
        
        Args:
            service: Service name
            
        Returns:
            True if service should be called, False if circuit is open
        """
        if service not in self.circuit_breakers:
            return True
        
        breaker = self.circuit_breakers[service]
        
        if breaker["state"] == "open":
            return False
        
        breaker["failure_count"] += 1
        breaker["last_failure"] = time.time()
        
        if breaker["failure_count"] >= breaker["failure_threshold"]:
            breaker["state"] = "open"
            logger.warning(f"Circuit breaker {service} opened due to failures")
            return False
        
        return True
    
    def record_service_success(self, service: str) -> None:
        """Record service success and update circuit breaker."""
        if service not in self.circuit_breakers:
            return
        
        breaker = self.circuit_breakers[service]
        
        if breaker["state"] == "half_open":
            breaker["failure_count"] = max(0, breaker["failure_count"] - 1)
            
            if breaker["failure_count"] == 0:
                breaker["state"] = "closed"
                logger.info(f"Circuit breaker {service} closed after successful recovery")
        elif breaker["state"] == "closed":
            breaker["failure_count"] = max(0, breaker["failure_count"] - 1)
    
    @performance_monitor("backup_creation")
    async def create_backup_async(self, backup_type: str = "full") -> Dict[str, Any]:
        """
        Create system backup.
        
        Args:
            backup_type: Type of backup (full, incremental, differential)
            
        Returns:
            Backup information
        """
        try:
            backup_id = f"backup_{int(time.time())}"
            backup_timestamp = datetime.now()
            
            logger.info(f"Creating {backup_type} backup: {backup_id}")
            
            # Collect data to backup
            backup_data = await self._collect_backup_data(backup_type)
            
            # Create backup files
            backup_files = []
            for location in self.backup_locations:
                if location["enabled"]:
                    backup_file = await self._create_backup_file(backup_data, location, backup_id)
                    backup_files.append(backup_file)
            
            backup_info = {
                "backup_id": backup_id,
                "backup_type": backup_type,
                "timestamp": backup_timestamp.isoformat(),
                "size_bytes": len(json.dumps(backup_data, default=str)),
                "files": backup_files,
                "status": "completed"
            }
            
            logger.info(f"Backup completed: {backup_id}")
            return backup_info
            
        except Exception as e:
            logger.error(f"Backup creation failed: {e}")
            raise SystemError(f"Backup failed: {e}")
    
    async def _collect_backup_data(self, backup_type: str) -> Dict[str, Any]:
        """Collect data for backup."""
        backup_data = {
            "metadata": {
                "backup_type": backup_type,
                "timestamp": datetime.now().isoformat(),
                "version": "3.0"
            },
            "system_states": self.system_states[-10:],  # Last 10 states
            "failure_events": self.failure_events[-50:],  # Last 50 failures
            "configuration": {
                "recovery_actions": {k: {
                    "action_id": v.action_id,
                    "trigger_conditions": v.trigger_conditions,
                    "action_type": v.action_type,
                    "priority": v.priority
                } for k, v in self.recovery_actions.items()},
                "circuit_breakers": self.circuit_breakers
            }
        }
        
        if backup_type == "full":
            # Add more comprehensive data for full backup
            backup_data["performance_metrics"] = self.performance_monitor.get_performance_summary()
        
        return backup_data
    
    async def _create_backup_file(self, backup_data: Dict[str, Any], 
                                 location: Dict[str, Any], backup_id: str) -> Dict[str, Any]:
        """Create backup file at specified location."""
        if location["type"] == "local":
            backup_path = Path(location["path"]) / f"{backup_id}.json"
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Encrypt backup data
            encrypted_data = CryptoUtils.encrypt_data(backup_data, CryptoUtils.generate_encryption_key()[0])
            
            with open(backup_path, 'w') as f:
                json.dump({
                    "encrypted_data": encrypted_data.hex(),
                    "backup_id": backup_id
                }, f)
            
            return {
                "location": str(backup_path),
                "type": "local",
                "size_bytes": backup_path.stat().st_size
            }
        
        # Other backup types would be implemented here
        return {"location": "not_implemented", "type": location["type"]}
    
    async def _periodic_backup(self) -> None:
        """Perform periodic backups."""
        while self.monitoring_active:
            try:
                # Create incremental backup every hour
                await self.create_backup_async("incremental")
                
                # Wait 1 hour
                await asyncio.sleep(3600)
                
            except Exception as e:
                logger.error(f"Periodic backup error: {e}")
                await asyncio.sleep(1800)  # Wait 30 minutes on error
    
    async def _cleanup_old_data(self) -> None:
        """Clean up old data and logs."""
        while self.monitoring_active:
            try:
                # Clean old system states (keep last 100)
                if len(self.system_states) > 100:
                    self.system_states = self.system_states[-100:]
                
                # Clean old failure events (keep last 500)
                if len(self.failure_events) > 500:
                    self.failure_events = self.failure_events[-500:]
                
                # Clean old backup files
                await self._cleanup_old_backups()
                
                # Wait 24 hours
                await asyncio.sleep(86400)
                
            except Exception as e:
                logger.error(f"Data cleanup error: {e}")
                await asyncio.sleep(3600)  # Wait 1 hour on error
    
    async def _cleanup_old_backups(self) -> None:
        """Clean up old backup files."""
        for location in self.backup_locations:
            if not location["enabled"] or location["type"] != "local":
                continue
            
            try:
                backup_dir = Path(location["path"])
                if not backup_dir.exists():
                    continue
                
                cutoff_date = datetime.now() - timedelta(days=location["retention_days"])
                
                for backup_file in backup_dir.glob("backup_*.json"):
                    if backup_file.stat().st_mtime < cutoff_date.timestamp():
                        backup_file.unlink()
                        logger.info(f"Deleted old backup: {backup_file}")
                        
            except Exception as e:
                logger.error(f"Backup cleanup error for {location['path']}: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status."""
        recent_failures = [f for f in self.failure_events 
                          if (datetime.now() - f.timestamp).total_seconds() < 3600]
        
        return {
            "monitoring_active": self.monitoring_active,
            "system_health": "healthy" if len(recent_failures) == 0 else "degraded",
            "recent_failures": len(recent_failures),
            "circuit_breakers": {k: v["state"] for k, v in self.circuit_breakers.items()},
            "backup_locations": len([l for l in self.backup_locations if l["enabled"]]),
            "last_backup": "simulated",  # Would track actual last backup
            "recovery_actions_available": len(self.recovery_actions)
        }
