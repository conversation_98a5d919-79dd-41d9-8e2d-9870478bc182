"""
Configuration management for CryptoForensics

Provides comprehensive configuration management with validation,
environment variable support, and professional defaults.
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field, asdict
from pydantic import BaseModel, Field, field_validator, model_validator
import logging

logger = logging.getLogger(__name__)

class APIConfig(BaseModel):
    """API configuration settings."""
    base_url: str = Field(default="https://blockstream.info/api/", description="Base API URL")
    rate_limit_delay: float = Field(default=0.1, ge=0.01, le=5.0, description="Delay between API calls")
    max_retries: int = Field(default=3, ge=1, le=10, description="Maximum retry attempts")
    request_timeout: int = Field(default=30, ge=5, le=300, description="Request timeout in seconds")
    connection_pool_size: int = Field(default=10, ge=1, le=100, description="Connection pool size")
    enable_caching: bool = Field(default=True, description="Enable response caching")
    cache_ttl: int = Field(default=3600, ge=60, description="Cache TTL in seconds")

    # API Keys for premium services
    api_keys: Dict[str, str] = Field(default_factory=dict, description="API keys for various services")

    # Rate limiting configuration
    requests_per_minute: int = Field(default=60, ge=1, le=1000, description="Requests per minute limit")
    burst_limit: int = Field(default=10, ge=1, le=100, description="Burst request limit")

class InvestigationConfig(BaseModel):
    """Configuration for individual investigations."""

    # Investigation parameters
    max_depth: int = Field(default=5, ge=1, le=20, description="Maximum tracing depth")
    max_addresses: int = Field(default=1000, ge=10, le=10000, description="Maximum addresses to trace")
    max_transactions: int = Field(default=5000, ge=10, le=50000, description="Maximum transactions to analyze")

    # Output settings
    output_directory: str = Field(default="investigation_results", description="Output directory path")
    save_reports: bool = Field(default=True, description="Save investigation reports")
    save_visualizations: bool = Field(default=True, description="Save visualizations")
    save_evidence: bool = Field(default=True, description="Save evidence packages")

    # Analysis settings
    enable_pattern_analysis: bool = Field(default=True, description="Enable pattern analysis")
    enable_risk_assessment: bool = Field(default=True, description="Enable risk assessment")
    enable_ml_analysis: bool = Field(default=False, description="Enable ML-based analysis")
    enable_cross_chain: bool = Field(default=False, description="Enable cross-chain analysis")

    # Thresholds
    suspicious_amount_threshold: float = Field(default=10.0, ge=0.001, description="Suspicious amount threshold (BTC)")
    high_risk_score_threshold: int = Field(default=8, ge=1, le=20, description="High risk score threshold")
    clustering_similarity_threshold: float = Field(default=0.8, ge=0.1, le=1.0, description="Clustering similarity threshold")

    # Performance settings
    enable_parallel_processing: bool = Field(default=True, description="Enable parallel processing")
    max_workers: int = Field(default=4, ge=1, le=32, description="Maximum worker threads")
    memory_limit_mb: int = Field(default=2048, ge=512, le=16384, description="Memory limit in MB")

    # Legal and compliance
    evidence_integrity_checks: bool = Field(default=True, description="Enable evidence integrity checks")
    audit_trail_enabled: bool = Field(default=True, description="Enable audit trail")
    chain_of_custody_required: bool = Field(default=True, description="Require chain of custody")

    @field_validator('output_directory')
    @classmethod
    def validate_output_directory(cls, v):
        """Validate and create output directory."""
        path = Path(v)
        path.mkdir(parents=True, exist_ok=True)
        return str(path.absolute())

    @model_validator(mode='after')
    def validate_thresholds(self):
        """Validate threshold relationships."""
        if self.max_addresses > self.max_transactions:
            raise ValueError("max_addresses cannot exceed max_transactions")
        return self

class SecurityConfig(BaseModel):
    """Security and encryption configuration."""

    # Encryption settings
    enable_encryption: bool = Field(default=True, description="Enable data encryption")
    encryption_algorithm: str = Field(default="AES-256-GCM", description="Encryption algorithm")
    key_derivation_iterations: int = Field(default=100000, ge=10000, description="Key derivation iterations")

    # Access control
    require_authentication: bool = Field(default=False, description="Require user authentication")
    session_timeout: int = Field(default=3600, ge=300, description="Session timeout in seconds")
    max_failed_attempts: int = Field(default=3, ge=1, le=10, description="Maximum failed login attempts")

    # Data protection
    anonymize_addresses: bool = Field(default=False, description="Anonymize addresses in logs")
    secure_delete: bool = Field(default=True, description="Secure deletion of temporary files")
    audit_all_access: bool = Field(default=True, description="Audit all data access")

class LoggingConfig(BaseModel):
    """Logging configuration."""

    level: str = Field(default="INFO", description="Logging level")
    format: str = Field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s", description="Log format")
    file_path: str = Field(default="logs/cryptoforensics.log", description="Log file path")
    max_file_size: int = Field(default=10485760, ge=1048576, description="Max log file size in bytes")
    backup_count: int = Field(default=5, ge=1, le=20, description="Number of backup log files")
    enable_console: bool = Field(default=True, description="Enable console logging")
    enable_file: bool = Field(default=True, description="Enable file logging")
    enable_structured: bool = Field(default=False, description="Enable structured logging")

    @field_validator('level')
    @classmethod
    def validate_level(cls, v):
        """Validate logging level."""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid logging level. Must be one of: {valid_levels}")
        return v.upper()

class GlobalConfig(BaseModel):
    """Global configuration for CryptoForensics."""

    # Sub-configurations
    api: APIConfig = Field(default_factory=APIConfig)
    investigation: InvestigationConfig = Field(default_factory=InvestigationConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)

    # Global settings
    version: str = Field(default="3.0.0", description="Application version")
    environment: str = Field(default="production", description="Environment (development/staging/production)")
    debug_mode: bool = Field(default=False, description="Enable debug mode")

    # Blockchain network settings
    supported_networks: List[str] = Field(
        default=["bitcoin", "ethereum", "litecoin", "bitcoin_cash"],
        description="Supported blockchain networks"
    )
    default_network: str = Field(default="bitcoin", description="Default blockchain network")

    # External service integrations
    enable_external_apis: bool = Field(default=True, description="Enable external API integrations")
    external_services: Dict[str, Dict[str, Any]] = Field(
        default_factory=lambda: {
            "chainalysis": {"enabled": False, "api_key": ""},
            "elliptic": {"enabled": False, "api_key": ""},
            "crystal": {"enabled": False, "api_key": ""},
        },
        description="External service configurations"
    )

    @classmethod
    def load_from_file(cls, config_path: Union[str, Path]) -> 'GlobalConfig':
        """Load configuration from file."""
        config_path = Path(config_path)

        if not config_path.exists():
            logger.warning(f"Config file not found: {config_path}. Using defaults.")
            return cls()

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    data = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    data = json.load(f)
                else:
                    raise ValueError(f"Unsupported config file format: {config_path.suffix}")

            return cls(**data)

        except Exception as e:
            logger.error(f"Error loading config from {config_path}: {e}")
            logger.info("Using default configuration")
            return cls()

    @classmethod
    def load_from_env(cls) -> 'GlobalConfig':
        """Load configuration from environment variables."""
        config_data = {}

        # Map environment variables to config structure
        env_mappings = {
            'CRYPTO_API_BASE_URL': ('api', 'base_url'),
            'CRYPTO_API_RATE_LIMIT': ('api', 'rate_limit_delay'),
            'CRYPTO_MAX_DEPTH': ('investigation', 'max_depth'),
            'CRYPTO_OUTPUT_DIR': ('investigation', 'output_directory'),
            'CRYPTO_LOG_LEVEL': ('logging', 'level'),
            'CRYPTO_DEBUG': ('debug_mode',),
            'CRYPTO_ENVIRONMENT': ('environment',),
        }

        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                # Navigate to the correct nested structure
                current = config_data
                for key in config_path[:-1]:
                    if key not in current:
                        current[key] = {}
                    current = current[key]

                # Convert value to appropriate type
                final_key = config_path[-1]
                if value.lower() in ['true', 'false']:
                    current[final_key] = value.lower() == 'true'
                elif value.isdigit():
                    current[final_key] = int(value)
                elif '.' in value and value.replace('.', '').isdigit():
                    current[final_key] = float(value)
                else:
                    current[final_key] = value

        return cls(**config_data)

    def save_to_file(self, config_path: Union[str, Path], format: str = 'yaml') -> None:
        """Save configuration to file."""
        config_path = Path(config_path)
        config_path.parent.mkdir(parents=True, exist_ok=True)

        data = self.model_dump()

        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                if format.lower() in ['yaml', 'yml']:
                    yaml.dump(data, f, default_flow_style=False, indent=2)
                elif format.lower() == 'json':
                    json.dump(data, f, indent=2, default=str)
                else:
                    raise ValueError(f"Unsupported format: {format}")

            logger.info(f"Configuration saved to {config_path}")

        except Exception as e:
            logger.error(f"Error saving config to {config_path}: {e}")
            raise

    def validate_configuration(self) -> List[str]:
        """Validate configuration and return list of issues."""
        issues = []

        # Check API configuration
        if not self.api.base_url.startswith(('http://', 'https://')):
            issues.append("API base URL must start with http:// or https://")

        # Check output directory permissions
        try:
            output_path = Path(self.investigation.output_directory)
            output_path.mkdir(parents=True, exist_ok=True)
            test_file = output_path / '.test_write'
            test_file.write_text('test')
            test_file.unlink()
        except Exception:
            issues.append(f"Cannot write to output directory: {self.investigation.output_directory}")

        # Check memory limits
        if self.investigation.memory_limit_mb < 512:
            issues.append("Memory limit too low, minimum 512MB required")

        # Check worker count vs system capabilities
        import multiprocessing
        max_workers = multiprocessing.cpu_count()
        if self.investigation.max_workers > max_workers * 2:
            issues.append(f"Max workers ({self.investigation.max_workers}) exceeds recommended limit ({max_workers * 2})")

        return issues

# Default configuration instance
default_config = GlobalConfig()

# Configuration factory functions
def create_investigation_config(**kwargs) -> InvestigationConfig:
    """Create investigation configuration with custom parameters."""
    return InvestigationConfig(**kwargs)

def create_api_config(**kwargs) -> APIConfig:
    """Create API configuration with custom parameters."""
    return APIConfig(**kwargs)

def load_config(config_path: Optional[Union[str, Path]] = None) -> GlobalConfig:
    """Load configuration from file or environment."""
    if config_path:
        return GlobalConfig.load_from_file(config_path)

    # Try to load from standard locations
    standard_paths = [
        Path.cwd() / 'cryptoforensics.yaml',
        Path.cwd() / 'config.yaml',
        Path.home() / '.cryptoforensics' / 'config.yaml',
        Path('/etc/cryptoforensics/config.yaml'),
    ]

    for path in standard_paths:
        if path.exists():
            return GlobalConfig.load_from_file(path)

    # Fall back to environment variables
    return GlobalConfig.load_from_env()
