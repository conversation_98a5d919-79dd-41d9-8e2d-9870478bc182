"""
Exception classes for CryptoForensics

Defines all custom exception classes used throughout the package.
"""

class CryptoForensicsError(Exception):
    """Base exception for CryptoForensics package."""
    pass

class ValidationError(CryptoForensicsError):
    """Raised when input validation fails."""
    pass

class APIError(CryptoForensicsError):
    """Raised when API operations fail."""
    pass

class EvidenceError(CryptoForensicsError):
    """Raised when evidence collection or integrity issues occur."""
    pass

class AnalysisError(CryptoForensicsError):
    """Raised when analysis operations fail."""
    pass
