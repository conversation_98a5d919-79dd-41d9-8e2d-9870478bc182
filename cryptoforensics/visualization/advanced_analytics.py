"""
Advanced analytics and machine learning for CryptoForensics

Provides behavioral pattern recognition, anomaly detection algorithms,
predictive analysis, and statistical correlation analysis for cryptocurrency investigations.
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timed<PERSON>ta
from sklearn.cluster import DBSCAN, KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.ensemble import IsolationForest
from sklearn.metrics import silhouette_score
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

from ..models import TransactionInfo
from ..core.config import InvestigationConfig
from ..exceptions import AnalysisError
from ..utils.performance import performance_monitor
from ..utils.time_utils import TimeUtils

logger = logging.getLogger(__name__)

@dataclass
class BehavioralPattern:
    """Detected behavioral pattern."""
    pattern_id: str
    pattern_type: str
    confidence: float
    description: str
    addresses: List[str]
    transactions: List[str]
    characteristics: Dict[str, Any]
    risk_score: float
    temporal_features: Dict[str, Any]

@dataclass
class AnomalyResult:
    """Anomaly detection result."""
    anomaly_id: str
    anomaly_type: str
    severity: str
    confidence: float
    affected_entities: List[str]
    anomaly_score: float
    features: Dict[str, Any]
    explanation: str

@dataclass
class PredictionResult:
    """Predictive analysis result."""
    prediction_id: str
    prediction_type: str
    target_entity: str
    predicted_value: float
    confidence_interval: Tuple[float, float]
    probability: float
    time_horizon: str
    features_used: List[str]

@dataclass
class CorrelationAnalysis:
    """Statistical correlation analysis result."""
    correlation_id: str
    entity_pairs: List[Tuple[str, str]]
    correlation_coefficient: float
    p_value: float
    significance_level: str
    correlation_type: str
    evidence: Dict[str, Any]

class AdvancedAnalytics:
    """
    Advanced analytics and machine learning system.

    Provides sophisticated analysis including:
    - Behavioral pattern recognition using ML
    - Anomaly detection with multiple algorithms
    - Predictive analysis for fund movement
    - Statistical correlation analysis
    - Clustering and classification
    """

    def __init__(self, config: InvestigationConfig):
        """
        Initialize advanced analytics system.

        Args:
            config: Investigation configuration
        """
        self.config = config
        self.models = {}
        self.feature_scalers = {}
        self.analysis_cache = {}

        # Initialize ML models
        self._initialize_models()

        logger.info("Advanced analytics system initialized")

    def _initialize_models(self) -> None:
        """Initialize machine learning models."""
        self.models = {
            "anomaly_detector": IsolationForest(
                contamination=0.1,
                random_state=42,
                n_estimators=100
            ),
            "clustering": {
                "dbscan": DBSCAN(eps=0.5, min_samples=5),
                "kmeans": KMeans(n_clusters=5, random_state=42)
            },
            "dimensionality_reduction": PCA(n_components=3),
            "feature_scaler": StandardScaler()
        }

    @performance_monitor("behavioral_pattern_recognition")
    async def detect_behavioral_patterns_async(self, transactions: List[TransactionInfo]) -> List[BehavioralPattern]:
        """
        Detect behavioral patterns using machine learning.

        Args:
            transactions: List of transactions to analyze

        Returns:
            List of detected behavioral patterns
        """
        try:
            if not transactions:
                return []

            logger.info(f"Detecting behavioral patterns in {len(transactions)} transactions")

            # Extract features for pattern recognition
            features_df = await self._extract_behavioral_features(transactions)

            # Apply clustering to identify patterns
            patterns = await self._apply_clustering_analysis(features_df, transactions)

            # Analyze temporal patterns
            temporal_patterns = await self._analyze_temporal_patterns(transactions)
            patterns.extend(temporal_patterns)

            # Analyze amount patterns
            amount_patterns = await self._analyze_amount_patterns(transactions)
            patterns.extend(amount_patterns)

            # Analyze network patterns
            network_patterns = await self._analyze_network_patterns(transactions)
            patterns.extend(network_patterns)

            logger.info(f"Detected {len(patterns)} behavioral patterns")
            return patterns

        except Exception as e:
            logger.error(f"Behavioral pattern detection failed: {e}")
            raise AnalysisError(f"Pattern detection failed: {e}")

    async def _extract_behavioral_features(self, transactions: List[TransactionInfo]) -> pd.DataFrame:
        """Extract behavioral features from transactions."""
        features = []

        # Group transactions by address
        address_groups = {}
        for tx in transactions:
            if tx.from_address not in address_groups:
                address_groups[tx.from_address] = []
            address_groups[tx.from_address].append(tx)

        for address, txs in address_groups.items():
            if len(txs) < 2:  # Need at least 2 transactions for pattern analysis
                continue

            # Calculate behavioral features
            amounts = [tx.amount_btc for tx in txs]
            timestamps = [TimeUtils.parse_timestamp(tx.timestamp) for tx in txs]
            valid_timestamps = [ts for ts in timestamps if ts is not None]

            if len(valid_timestamps) < 2:
                continue

            # Temporal features
            intervals = []
            for i in range(1, len(valid_timestamps)):
                interval = (valid_timestamps[i] - valid_timestamps[i-1]).total_seconds()
                intervals.append(interval)

            # Amount features
            amount_mean = np.mean(amounts)
            amount_std = np.std(amounts)
            amount_cv = amount_std / amount_mean if amount_mean > 0 else 0

            # Timing features
            interval_mean = np.mean(intervals) if intervals else 0
            interval_std = np.std(intervals) if len(intervals) > 1 else 0
            interval_cv = interval_std / interval_mean if interval_mean > 0 else 0

            # Activity features
            tx_count = len(txs)
            unique_destinations = len(set(tx.to_address for tx in txs))
            destination_diversity = unique_destinations / tx_count

            # Time-of-day features
            hours = [ts.hour for ts in valid_timestamps]
            hour_entropy = self._calculate_entropy(hours)

            # Day-of-week features
            weekdays = [ts.weekday() for ts in valid_timestamps]
            weekday_entropy = self._calculate_entropy(weekdays)

            features.append({
                'address': address,
                'tx_count': tx_count,
                'amount_mean': amount_mean,
                'amount_std': amount_std,
                'amount_cv': amount_cv,
                'interval_mean': interval_mean,
                'interval_std': interval_std,
                'interval_cv': interval_cv,
                'destination_diversity': destination_diversity,
                'hour_entropy': hour_entropy,
                'weekday_entropy': weekday_entropy,
                'total_volume': sum(amounts),
                'max_amount': max(amounts),
                'min_amount': min(amounts)
            })

        return pd.DataFrame(features)

    def _calculate_entropy(self, values: List[int]) -> float:
        """Calculate entropy of a list of values."""
        if not values:
            return 0.0

        value_counts = pd.Series(values).value_counts()
        probabilities = value_counts / len(values)
        entropy = -sum(p * np.log2(p) for p in probabilities if p > 0)
        return entropy

    async def _apply_clustering_analysis(self, features_df: pd.DataFrame,
                                       transactions: List[TransactionInfo]) -> List[BehavioralPattern]:
        """Apply clustering analysis to identify behavioral patterns."""
        if features_df.empty:
            return []

        patterns = []

        # Prepare features for clustering
        feature_columns = [col for col in features_df.columns if col != 'address']
        X = features_df[feature_columns].fillna(0)

        # Scale features
        X_scaled = self.models["feature_scaler"].fit_transform(X)

        # Apply DBSCAN clustering
        dbscan = self.models["clustering"]["dbscan"]
        cluster_labels = dbscan.fit_predict(X_scaled)

        # Analyze clusters
        unique_labels = set(cluster_labels)
        for label in unique_labels:
            if label == -1:  # Noise points
                continue

            cluster_mask = cluster_labels == label
            cluster_addresses = features_df.loc[cluster_mask, 'address'].tolist()

            if len(cluster_addresses) < 2:
                continue

            # Calculate cluster characteristics
            cluster_features = X[cluster_mask]
            cluster_center = cluster_features.mean()
            cluster_std = cluster_features.std()

            # Determine pattern type based on characteristics
            pattern_type = self._classify_cluster_pattern(cluster_center, cluster_std, feature_columns)

            # Calculate confidence based on cluster cohesion
            if len(cluster_addresses) > 1:
                silhouette = silhouette_score(X_scaled, cluster_labels)
                confidence = max(0.0, min(1.0, silhouette))
            else:
                confidence = 0.5

            # Get transactions for this cluster
            cluster_transactions = []
            for tx in transactions:
                if tx.from_address in cluster_addresses:
                    cluster_transactions.append(tx.txid)

            pattern = BehavioralPattern(
                pattern_id=f"cluster_{label}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                pattern_type=pattern_type,
                confidence=confidence,
                description=f"Behavioral cluster with {len(cluster_addresses)} addresses",
                addresses=cluster_addresses,
                transactions=cluster_transactions,
                characteristics={
                    'cluster_size': len(cluster_addresses),
                    'avg_tx_count': cluster_center[feature_columns.index('tx_count')],
                    'avg_amount': cluster_center[feature_columns.index('amount_mean')],
                    'avg_interval': cluster_center[feature_columns.index('interval_mean')]
                },
                risk_score=self._calculate_pattern_risk_score(pattern_type, cluster_center),
                temporal_features={}
            )
            patterns.append(pattern)

        return patterns

    def _classify_cluster_pattern(self, cluster_center: pd.Series, cluster_std: pd.Series,
                                 feature_columns: List[str]) -> str:
        """Classify cluster pattern type based on characteristics."""
        # Get feature indices
        try:
            tx_count_idx = feature_columns.index('tx_count')
            interval_cv_idx = feature_columns.index('interval_cv')
            destination_diversity_idx = feature_columns.index('destination_diversity')
            amount_cv_idx = feature_columns.index('amount_cv')
        except ValueError:
            return "unknown"

        # High transaction count + low interval CV = automated behavior
        if cluster_center[tx_count_idx] > 10 and cluster_center[interval_cv_idx] < 0.3:
            return "automated_behavior"

        # High destination diversity = distribution pattern
        if cluster_center[destination_diversity_idx] > 0.7:
            return "distribution_pattern"

        # Low amount CV = consistent amounts (possible mixing)
        if cluster_center[amount_cv_idx] < 0.2:
            return "consistent_amounts"

        # High transaction count + high destination diversity = hub behavior
        if cluster_center[tx_count_idx] > 20 and cluster_center[destination_diversity_idx] > 0.5:
            return "hub_behavior"

        return "general_pattern"

    def _calculate_pattern_risk_score(self, pattern_type: str, cluster_center: pd.Series) -> float:
        """Calculate risk score for a behavioral pattern."""
        base_scores = {
            "automated_behavior": 0.7,
            "distribution_pattern": 0.6,
            "consistent_amounts": 0.8,
            "hub_behavior": 0.5,
            "general_pattern": 0.3,
            "unknown": 0.2
        }

        base_score = base_scores.get(pattern_type, 0.3)

        # Adjust based on transaction volume and frequency
        # This is a simplified risk calculation
        return min(1.0, base_score)

    async def _analyze_temporal_patterns(self, transactions: List[TransactionInfo]) -> List[BehavioralPattern]:
        """Analyze temporal patterns in transactions."""
        patterns = []

        # Group transactions by hour of day
        hour_groups = {}
        for tx in transactions:
            ts = TimeUtils.parse_timestamp(tx.timestamp)
            if ts:
                hour = ts.hour
                if hour not in hour_groups:
                    hour_groups[hour] = []
                hour_groups[hour].append(tx)

        # Find hours with unusually high activity
        if hour_groups:
            hour_counts = {hour: len(txs) for hour, txs in hour_groups.items()}
            mean_count = np.mean(list(hour_counts.values()))
            std_count = np.std(list(hour_counts.values()))

            for hour, count in hour_counts.items():
                if count > mean_count + 2 * std_count:  # 2 standard deviations above mean
                    affected_txs = hour_groups[hour]
                    addresses = list(set(tx.from_address for tx in affected_txs))

                    pattern = BehavioralPattern(
                        pattern_id=f"temporal_hour_{hour}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        pattern_type="temporal_clustering",
                        confidence=0.7,
                        description=f"High activity during hour {hour}:00",
                        addresses=addresses,
                        transactions=[tx.txid for tx in affected_txs],
                        characteristics={
                            'peak_hour': hour,
                            'transaction_count': count,
                            'deviation_from_mean': (count - mean_count) / std_count
                        },
                        risk_score=0.4,
                        temporal_features={
                            'hour_concentration': count / len(transactions),
                            'peak_hour': hour
                        }
                    )
                    patterns.append(pattern)

        return patterns

    async def _analyze_amount_patterns(self, transactions: List[TransactionInfo]) -> List[BehavioralPattern]:
        """Analyze amount patterns in transactions."""
        patterns = []

        amounts = [tx.amount_btc for tx in transactions]
        if not amounts:
            return patterns

        # Detect round amounts (potential mixing indicator)
        round_amounts = []
        for i, tx in enumerate(transactions):
            amount = tx.amount_btc
            # Check if amount is "round" (few decimal places)
            if amount == round(amount, 2):  # Round to 2 decimal places
                round_amounts.append((i, tx))

        if len(round_amounts) > len(transactions) * 0.3:  # More than 30% round amounts
            addresses = list(set(tx.from_address for _, tx in round_amounts))

            pattern = BehavioralPattern(
                pattern_id=f"round_amounts_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                pattern_type="round_amounts",
                confidence=0.6,
                description="High frequency of round amount transactions",
                addresses=addresses,
                transactions=[tx.txid for _, tx in round_amounts],
                characteristics={
                    'round_amount_ratio': len(round_amounts) / len(transactions),
                    'round_amount_count': len(round_amounts)
                },
                risk_score=0.6,
                temporal_features={}
            )
            patterns.append(pattern)

        return patterns

    async def _analyze_network_patterns(self, transactions: List[TransactionInfo]) -> List[BehavioralPattern]:
        """Analyze network connectivity patterns."""
        patterns = []

        # Build address connectivity graph
        connections = {}
        for tx in transactions:
            if tx.from_address not in connections:
                connections[tx.from_address] = set()
            connections[tx.from_address].add(tx.to_address)

        # Find highly connected addresses (hubs)
        connection_counts = {addr: len(targets) for addr, targets in connections.items()}
        if connection_counts:
            mean_connections = np.mean(list(connection_counts.values()))
            std_connections = np.std(list(connection_counts.values()))

            hub_addresses = []
            for addr, count in connection_counts.items():
                if count > mean_connections + 2 * std_connections:
                    hub_addresses.append(addr)

            if hub_addresses:
                hub_transactions = [tx.txid for tx in transactions
                                  if tx.from_address in hub_addresses]

                pattern = BehavioralPattern(
                    pattern_id=f"network_hubs_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    pattern_type="network_hubs",
                    confidence=0.8,
                    description=f"Detected {len(hub_addresses)} network hub addresses",
                    addresses=hub_addresses,
                    transactions=hub_transactions,
                    characteristics={
                        'hub_count': len(hub_addresses),
                        'avg_connections': np.mean([connection_counts[addr] for addr in hub_addresses]),
                        'max_connections': max(connection_counts[addr] for addr in hub_addresses)
                    },
                    risk_score=0.5,
                    temporal_features={}
                )
                patterns.append(pattern)

        return patterns

    @performance_monitor("anomaly_detection")
    async def detect_anomalies_async(self, transactions: List[TransactionInfo]) -> List[AnomalyResult]:
        """
        Detect anomalies using multiple algorithms.

        Args:
            transactions: List of transactions to analyze

        Returns:
            List of detected anomalies
        """
        try:
            if not transactions:
                return []

            logger.info(f"Detecting anomalies in {len(transactions)} transactions")

            # Extract features for anomaly detection
            features_df = await self._extract_anomaly_features(transactions)

            if features_df.empty:
                return []

            # Apply isolation forest
            anomalies = await self._apply_isolation_forest(features_df, transactions)

            # Apply statistical outlier detection
            statistical_anomalies = await self._apply_statistical_outlier_detection(features_df, transactions)
            anomalies.extend(statistical_anomalies)

            logger.info(f"Detected {len(anomalies)} anomalies")
            return anomalies

        except Exception as e:
            logger.error(f"Anomaly detection failed: {e}")
            raise AnalysisError(f"Anomaly detection failed: {e}")

    async def _extract_anomaly_features(self, transactions: List[TransactionInfo]) -> pd.DataFrame:
        """Extract features for anomaly detection."""
        features = []

        for tx in transactions:
            ts = TimeUtils.parse_timestamp(tx.timestamp)

            feature_row = {
                'txid': tx.txid,
                'amount': tx.amount_btc,
                'hour': ts.hour if ts else 12,
                'weekday': ts.weekday() if ts else 0,
                'depth': tx.depth,
                'confirmations': tx.confirmations,
                'from_address': tx.from_address,
                'to_address': tx.to_address
            }
            features.append(feature_row)

        return pd.DataFrame(features)

    async def _apply_isolation_forest(self, features_df: pd.DataFrame,
                                    transactions: List[TransactionInfo]) -> List[AnomalyResult]:
        """Apply Isolation Forest for anomaly detection."""
        anomalies = []

        # Prepare numerical features
        numerical_features = ['amount', 'hour', 'weekday', 'depth', 'confirmations']
        X = features_df[numerical_features].fillna(0)

        # Apply Isolation Forest
        iso_forest = self.models["anomaly_detector"]
        anomaly_scores = iso_forest.fit_predict(X)
        anomaly_scores_continuous = iso_forest.decision_function(X)

        # Find anomalies
        for i, (score, continuous_score) in enumerate(zip(anomaly_scores, anomaly_scores_continuous)):
            if score == -1:  # Anomaly detected
                tx = transactions[i]

                anomaly = AnomalyResult(
                    anomaly_id=f"iso_forest_{tx.txid}",
                    anomaly_type="statistical_outlier",
                    severity="medium",
                    confidence=abs(continuous_score),
                    affected_entities=[tx.txid],
                    anomaly_score=continuous_score,
                    features={
                        'amount': tx.amount_btc,
                        'depth': tx.depth,
                        'confirmations': tx.confirmations
                    },
                    explanation=f"Transaction {tx.txid} identified as statistical outlier"
                )
                anomalies.append(anomaly)

        return anomalies

    async def _apply_statistical_outlier_detection(self, features_df: pd.DataFrame,
                                                  transactions: List[TransactionInfo]) -> List[AnomalyResult]:
        """Apply statistical methods for outlier detection."""
        anomalies = []

        # Z-score based outlier detection for amounts
        amounts = features_df['amount']
        if len(amounts) > 1:
            z_scores = np.abs((amounts - amounts.mean()) / amounts.std())

            for i, z_score in enumerate(z_scores):
                if z_score > 3:  # 3 standard deviations
                    tx = transactions[i]

                    anomaly = AnomalyResult(
                        anomaly_id=f"z_score_{tx.txid}",
                        anomaly_type="amount_outlier",
                        severity="high" if z_score > 4 else "medium",
                        confidence=min(1.0, z_score / 5),
                        affected_entities=[tx.txid],
                        anomaly_score=z_score,
                        features={'amount': tx.amount_btc, 'z_score': z_score},
                        explanation=f"Transaction amount {tx.amount_btc} BTC is {z_score:.2f} standard deviations from mean"
                    )
                    anomalies.append(anomaly)

        return anomalies
