"""
Advanced 3D visualization for CryptoForensics

Provides interactive 3D transaction flow graphs, network topology visualization,
and immersive analysis environments for cryptocurrency investigations.
"""

import asyncio
import logging
import json
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import networkx as nx

from ..models import TransactionInfo
from ..core.config import InvestigationConfig
from ..exceptions import VisualizationError
from ..utils.performance import performance_monitor
from ..utils.time_utils import TimeUtils

logger = logging.getLogger(__name__)

@dataclass
class Node3D:
    """3D node representation."""
    id: str
    x: float
    y: float
    z: float
    size: float
    color: str
    label: str
    node_type: str
    metadata: Dict[str, Any]

@dataclass
class Edge3D:
    """3D edge representation."""
    source: str
    target: str
    weight: float
    color: str
    width: float
    edge_type: str
    metadata: Dict[str, Any]

@dataclass
class Visualization3DConfig:
    """3D visualization configuration."""
    layout_algorithm: str = "force_directed_3d"
    node_size_factor: float = 1.0
    edge_width_factor: float = 1.0
    color_scheme: str = "risk_based"
    animation_enabled: bool = True
    interactive_mode: bool = True
    clustering_enabled: bool = True
    time_dimension: bool = True

class Advanced3DVisualizer:
    """
    Advanced 3D visualization system for cryptocurrency investigations.
    
    Provides immersive 3D visualizations including:
    - Interactive 3D transaction flow graphs
    - Network topology visualization with clustering
    - Time-based animation of transaction flows
    - Risk-based color coding and sizing
    - Multi-dimensional analysis views
    """
    
    def __init__(self, config: InvestigationConfig):
        """
        Initialize 3D visualizer.
        
        Args:
            config: Investigation configuration
        """
        self.config = config
        self.visualization_cache = {}
        self.color_schemes = self._initialize_color_schemes()
        self.layout_algorithms = self._initialize_layout_algorithms()
        
        logger.info("Advanced 3D visualizer initialized")
    
    def _initialize_color_schemes(self) -> Dict[str, Dict[str, str]]:
        """Initialize color schemes for different visualization types."""
        return {
            "risk_based": {
                "low_risk": "#2E8B57",      # Sea Green
                "medium_risk": "#FFD700",   # Gold
                "high_risk": "#FF6347",     # Tomato
                "critical_risk": "#DC143C", # Crimson
                "unknown": "#708090",       # Slate Gray
                "mixer": "#8A2BE2",         # Blue Violet
                "exchange": "#4169E1",      # Royal Blue
                "wallet": "#32CD32"         # Lime Green
            },
            "transaction_type": {
                "normal": "#87CEEB",        # Sky Blue
                "suspicious": "#FF4500",    # Orange Red
                "mixing": "#9932CC",        # Dark Orchid
                "consolidation": "#20B2AA", # Light Sea Green
                "distribution": "#FF1493",  # Deep Pink
                "peel_chain": "#B22222"     # Fire Brick
            },
            "temporal": {
                "recent": "#00FF00",        # Lime
                "medium": "#FFFF00",        # Yellow
                "old": "#FF0000",           # Red
                "ancient": "#800080"        # Purple
            }
        }
    
    def _initialize_layout_algorithms(self) -> Dict[str, callable]:
        """Initialize 3D layout algorithms."""
        return {
            "force_directed_3d": self._force_directed_3d_layout,
            "hierarchical_3d": self._hierarchical_3d_layout,
            "circular_3d": self._circular_3d_layout,
            "cluster_based_3d": self._cluster_based_3d_layout,
            "temporal_3d": self._temporal_3d_layout
        }
    
    @performance_monitor("3d_visualization")
    async def create_3d_transaction_flow_async(self, transactions: List[TransactionInfo],
                                             config: Visualization3DConfig) -> Dict[str, Any]:
        """
        Create interactive 3D transaction flow visualization.
        
        Args:
            transactions: List of transactions to visualize
            config: 3D visualization configuration
            
        Returns:
            3D visualization data and metadata
        """
        try:
            if not transactions:
                raise VisualizationError("No transactions provided for visualization")
            
            logger.info(f"Creating 3D transaction flow for {len(transactions)} transactions")
            
            # Build transaction graph
            graph = self._build_transaction_graph(transactions)
            
            # Apply 3D layout algorithm
            layout_func = self.layout_algorithms.get(config.layout_algorithm, 
                                                   self._force_directed_3d_layout)
            nodes_3d, edges_3d = await layout_func(graph, transactions, config)
            
            # Create interactive 3D plot
            fig = self._create_plotly_3d_graph(nodes_3d, edges_3d, config)
            
            # Add time animation if enabled
            if config.animation_enabled and config.time_dimension:
                fig = self._add_temporal_animation(fig, transactions, nodes_3d, edges_3d)
            
            # Generate visualization metadata
            metadata = self._generate_visualization_metadata(transactions, nodes_3d, edges_3d, config)
            
            visualization_data = {
                "figure": fig,
                "nodes": [node.__dict__ for node in nodes_3d],
                "edges": [edge.__dict__ for edge in edges_3d],
                "metadata": metadata,
                "config": config.__dict__
            }
            
            logger.info("3D transaction flow visualization created successfully")
            return visualization_data
            
        except Exception as e:
            logger.error(f"3D visualization creation failed: {e}")
            raise VisualizationError(f"3D visualization failed: {e}")
    
    def _build_transaction_graph(self, transactions: List[TransactionInfo]) -> nx.DiGraph:
        """Build NetworkX graph from transactions."""
        graph = nx.DiGraph()
        
        for tx in transactions:
            # Add nodes
            graph.add_node(tx.from_address, 
                          address_type="source",
                          total_sent=0,
                          total_received=0,
                          transaction_count=0)
            graph.add_node(tx.to_address,
                          address_type="destination", 
                          total_sent=0,
                          total_received=0,
                          transaction_count=0)
            
            # Add edge
            if graph.has_edge(tx.from_address, tx.to_address):
                # Update existing edge
                edge_data = graph[tx.from_address][tx.to_address]
                edge_data['weight'] += tx.amount_btc
                edge_data['transaction_count'] += 1
                edge_data['transactions'].append(tx.txid)
            else:
                # Create new edge
                graph.add_edge(tx.from_address, tx.to_address,
                              weight=tx.amount_btc,
                              transaction_count=1,
                              transactions=[tx.txid],
                              timestamp=tx.timestamp)
            
            # Update node statistics
            graph.nodes[tx.from_address]['total_sent'] += tx.amount_btc
            graph.nodes[tx.from_address]['transaction_count'] += 1
            graph.nodes[tx.to_address]['total_received'] += tx.amount_btc
            graph.nodes[tx.to_address]['transaction_count'] += 1
        
        return graph
    
    async def _force_directed_3d_layout(self, graph: nx.DiGraph, 
                                       transactions: List[TransactionInfo],
                                       config: Visualization3DConfig) -> Tuple[List[Node3D], List[Edge3D]]:
        """Create force-directed 3D layout."""
        # Use NetworkX spring layout as base, then extend to 3D
        pos_2d = nx.spring_layout(graph, k=1, iterations=50)
        
        nodes_3d = []
        edges_3d = []
        
        # Convert 2D positions to 3D and add z-dimension based on node importance
        for node_id, (x, y) in pos_2d.items():
            node_data = graph.nodes[node_id]
            
            # Calculate z-position based on transaction volume
            total_volume = node_data.get('total_sent', 0) + node_data.get('total_received', 0)
            z = np.log1p(total_volume) * 0.1  # Logarithmic scaling
            
            # Determine node size based on transaction count
            tx_count = node_data.get('transaction_count', 1)
            size = max(5, min(50, tx_count * 2))
            
            # Determine color based on risk assessment
            color = self._calculate_node_color(node_id, node_data, config.color_scheme)
            
            node_3d = Node3D(
                id=node_id,
                x=x,
                y=y,
                z=z,
                size=size * config.node_size_factor,
                color=color,
                label=f"{node_id[:8]}...{node_id[-8:]}",
                node_type=node_data.get('address_type', 'unknown'),
                metadata={
                    'total_sent': node_data.get('total_sent', 0),
                    'total_received': node_data.get('total_received', 0),
                    'transaction_count': tx_count,
                    'full_address': node_id
                }
            )
            nodes_3d.append(node_3d)
        
        # Create 3D edges
        for source, target, edge_data in graph.edges(data=True):
            weight = edge_data.get('weight', 0)
            tx_count = edge_data.get('transaction_count', 1)
            
            # Edge width based on transaction volume
            width = max(1, min(10, np.log1p(weight) * 2))
            
            # Edge color based on transaction characteristics
            color = self._calculate_edge_color(edge_data, config.color_scheme)
            
            edge_3d = Edge3D(
                source=source,
                target=target,
                weight=weight,
                color=color,
                width=width * config.edge_width_factor,
                edge_type="transaction_flow",
                metadata={
                    'transaction_count': tx_count,
                    'total_amount': weight,
                    'transactions': edge_data.get('transactions', [])
                }
            )
            edges_3d.append(edge_3d)
        
        return nodes_3d, edges_3d
    
    async def _hierarchical_3d_layout(self, graph: nx.DiGraph,
                                     transactions: List[TransactionInfo],
                                     config: Visualization3DConfig) -> Tuple[List[Node3D], List[Edge3D]]:
        """Create hierarchical 3D layout based on transaction flow."""
        # Implement hierarchical layout
        # For now, use force-directed as fallback
        return await self._force_directed_3d_layout(graph, transactions, config)
    
    async def _circular_3d_layout(self, graph: nx.DiGraph,
                                 transactions: List[TransactionInfo],
                                 config: Visualization3DConfig) -> Tuple[List[Node3D], List[Edge3D]]:
        """Create circular 3D layout."""
        # Implement circular layout
        # For now, use force-directed as fallback
        return await self._force_directed_3d_layout(graph, transactions, config)
    
    async def _cluster_based_3d_layout(self, graph: nx.DiGraph,
                                      transactions: List[TransactionInfo],
                                      config: Visualization3DConfig) -> Tuple[List[Node3D], List[Edge3D]]:
        """Create cluster-based 3D layout."""
        # Implement clustering-based layout
        # For now, use force-directed as fallback
        return await self._force_directed_3d_layout(graph, transactions, config)
    
    async def _temporal_3d_layout(self, graph: nx.DiGraph,
                                 transactions: List[TransactionInfo],
                                 config: Visualization3DConfig) -> Tuple[List[Node3D], List[Edge3D]]:
        """Create temporal 3D layout with time as z-axis."""
        # Parse timestamps and create temporal layout
        timestamps = []
        for tx in transactions:
            ts = TimeUtils.parse_timestamp(tx.timestamp)
            if ts:
                timestamps.append((tx, ts))
        
        if not timestamps:
            # Fallback to force-directed if no valid timestamps
            return await self._force_directed_3d_layout(graph, transactions, config)
        
        # Sort by timestamp
        timestamps.sort(key=lambda x: x[1])
        
        # Create 2D layout for x,y coordinates
        pos_2d = nx.spring_layout(graph, k=1, iterations=50)
        
        nodes_3d = []
        edges_3d = []
        
        # Map timestamps to z-coordinates
        min_time = timestamps[0][1]
        max_time = timestamps[-1][1]
        time_range = (max_time - min_time).total_seconds()
        
        # Create nodes with temporal z-positioning
        for node_id, (x, y) in pos_2d.items():
            node_data = graph.nodes[node_id]
            
            # Find earliest transaction for this node
            node_timestamps = []
            for tx, ts in timestamps:
                if tx.from_address == node_id or tx.to_address == node_id:
                    node_timestamps.append(ts)
            
            if node_timestamps:
                earliest_time = min(node_timestamps)
                z = ((earliest_time - min_time).total_seconds() / time_range) * 2.0 - 1.0
            else:
                z = 0
            
            # Calculate node properties
            tx_count = node_data.get('transaction_count', 1)
            size = max(5, min(50, tx_count * 2))
            color = self._calculate_node_color(node_id, node_data, config.color_scheme)
            
            node_3d = Node3D(
                id=node_id,
                x=x,
                y=y,
                z=z,
                size=size * config.node_size_factor,
                color=color,
                label=f"{node_id[:8]}...{node_id[-8:]}",
                node_type=node_data.get('address_type', 'unknown'),
                metadata={
                    'total_sent': node_data.get('total_sent', 0),
                    'total_received': node_data.get('total_received', 0),
                    'transaction_count': tx_count,
                    'earliest_transaction': earliest_time.isoformat() if node_timestamps else None
                }
            )
            nodes_3d.append(node_3d)
        
        # Create edges (same as force-directed)
        for source, target, edge_data in graph.edges(data=True):
            weight = edge_data.get('weight', 0)
            width = max(1, min(10, np.log1p(weight) * 2))
            color = self._calculate_edge_color(edge_data, config.color_scheme)
            
            edge_3d = Edge3D(
                source=source,
                target=target,
                weight=weight,
                color=color,
                width=width * config.edge_width_factor,
                edge_type="temporal_flow",
                metadata={
                    'transaction_count': edge_data.get('transaction_count', 1),
                    'total_amount': weight,
                    'timestamp': edge_data.get('timestamp')
                }
            )
            edges_3d.append(edge_3d)
        
        return nodes_3d, edges_3d
    
    def _calculate_node_color(self, node_id: str, node_data: Dict[str, Any], color_scheme: str) -> str:
        """Calculate node color based on characteristics."""
        colors = self.color_schemes.get(color_scheme, self.color_schemes["risk_based"])
        
        if color_scheme == "risk_based":
            # Simple risk assessment based on transaction volume
            total_volume = node_data.get('total_sent', 0) + node_data.get('total_received', 0)
            if total_volume > 100:
                return colors["critical_risk"]
            elif total_volume > 10:
                return colors["high_risk"]
            elif total_volume > 1:
                return colors["medium_risk"]
            else:
                return colors["low_risk"]
        
        return colors.get("unknown", "#708090")
    
    def _calculate_edge_color(self, edge_data: Dict[str, Any], color_scheme: str) -> str:
        """Calculate edge color based on characteristics."""
        colors = self.color_schemes.get(color_scheme, self.color_schemes["risk_based"])
        
        if color_scheme == "risk_based":
            weight = edge_data.get('weight', 0)
            if weight > 50:
                return colors["critical_risk"]
            elif weight > 10:
                return colors["high_risk"]
            elif weight > 1:
                return colors["medium_risk"]
            else:
                return colors["low_risk"]
        
        return colors.get("normal", "#87CEEB")
    
    def _create_plotly_3d_graph(self, nodes_3d: List[Node3D], edges_3d: List[Edge3D],
                               config: Visualization3DConfig) -> go.Figure:
        """Create interactive Plotly 3D graph."""
        fig = go.Figure()
        
        # Add edges as 3D lines
        for edge in edges_3d:
            source_node = next(n for n in nodes_3d if n.id == edge.source)
            target_node = next(n for n in nodes_3d if n.id == edge.target)
            
            fig.add_trace(go.Scatter3d(
                x=[source_node.x, target_node.x, None],
                y=[source_node.y, target_node.y, None],
                z=[source_node.z, target_node.z, None],
                mode='lines',
                line=dict(color=edge.color, width=edge.width),
                hoverinfo='skip',
                showlegend=False,
                name=f"Edge {edge.source[:8]}→{edge.target[:8]}"
            ))
        
        # Add nodes as 3D scatter
        node_x = [node.x for node in nodes_3d]
        node_y = [node.y for node in nodes_3d]
        node_z = [node.z for node in nodes_3d]
        node_colors = [node.color for node in nodes_3d]
        node_sizes = [node.size for node in nodes_3d]
        node_labels = [node.label for node in nodes_3d]
        
        # Create hover text
        hover_text = []
        for node in nodes_3d:
            hover_info = f"<b>{node.label}</b><br>"
            hover_info += f"Type: {node.node_type}<br>"
            hover_info += f"Transactions: {node.metadata.get('transaction_count', 0)}<br>"
            hover_info += f"Total Sent: {node.metadata.get('total_sent', 0):.8f} BTC<br>"
            hover_info += f"Total Received: {node.metadata.get('total_received', 0):.8f} BTC"
            hover_text.append(hover_info)
        
        fig.add_trace(go.Scatter3d(
            x=node_x,
            y=node_y,
            z=node_z,
            mode='markers',
            marker=dict(
                size=node_sizes,
                color=node_colors,
                opacity=0.8,
                line=dict(width=2, color='DarkSlateGrey')
            ),
            text=node_labels,
            hovertext=hover_text,
            hoverinfo='text',
            name="Addresses"
        ))
        
        # Update layout
        fig.update_layout(
            title="3D Transaction Flow Visualization",
            scene=dict(
                xaxis_title="X Coordinate",
                yaxis_title="Y Coordinate", 
                zaxis_title="Z Coordinate (Time/Volume)",
                camera=dict(
                    eye=dict(x=1.5, y=1.5, z=1.5)
                )
            ),
            showlegend=True,
            height=800,
            margin=dict(l=0, r=0, b=0, t=40)
        )
        
        return fig
    
    def _add_temporal_animation(self, fig: go.Figure, transactions: List[TransactionInfo],
                               nodes_3d: List[Node3D], edges_3d: List[Edge3D]) -> go.Figure:
        """Add temporal animation to the 3D visualization."""
        # This would implement frame-by-frame animation
        # For now, return the figure as-is
        return fig
    
    def _generate_visualization_metadata(self, transactions: List[TransactionInfo],
                                       nodes_3d: List[Node3D], edges_3d: List[Edge3D],
                                       config: Visualization3DConfig) -> Dict[str, Any]:
        """Generate metadata for the visualization."""
        return {
            "transaction_count": len(transactions),
            "node_count": len(nodes_3d),
            "edge_count": len(edges_3d),
            "layout_algorithm": config.layout_algorithm,
            "color_scheme": config.color_scheme,
            "total_volume": sum(tx.amount_btc for tx in transactions),
            "time_span": self._calculate_time_span(transactions),
            "network_density": len(edges_3d) / max(len(nodes_3d) * (len(nodes_3d) - 1), 1),
            "generated_at": datetime.now().isoformat()
        }
    
    def _calculate_time_span(self, transactions: List[TransactionInfo]) -> Dict[str, Any]:
        """Calculate time span of transactions."""
        timestamps = []
        for tx in transactions:
            ts = TimeUtils.parse_timestamp(tx.timestamp)
            if ts:
                timestamps.append(ts)
        
        if not timestamps:
            return {"error": "No valid timestamps"}
        
        min_time = min(timestamps)
        max_time = max(timestamps)
        span = max_time - min_time
        
        return {
            "start_time": min_time.isoformat(),
            "end_time": max_time.isoformat(),
            "duration_seconds": span.total_seconds(),
            "duration_hours": span.total_seconds() / 3600,
            "duration_days": span.days
        }
