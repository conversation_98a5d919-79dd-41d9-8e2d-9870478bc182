"""
Real-time monitoring dashboard for CryptoForensics

Provides live monitoring dashboards, real-time alerts, and dynamic
visualization updates for ongoing cryptocurrency investigations.
"""

import asyncio
import logging
import json
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np

from ..models import TransactionInfo
from ..core.config import InvestigationConfig
from ..exceptions import VisualizationError
from ..utils.performance import performance_monitor, PerformanceMonitor
from ..utils.time_utils import TimeUtils

logger = logging.getLogger(__name__)

@dataclass
class DashboardMetric:
    """Real-time dashboard metric."""
    metric_id: str
    name: str
    value: float
    unit: str
    trend: str  # "up", "down", "stable"
    change_percent: float
    threshold_warning: Optional[float] = None
    threshold_critical: Optional[float] = None
    last_updated: str = field(default_factory=lambda: datetime.now().isoformat())

@dataclass
class AlertRule:
    """Alert rule configuration."""
    rule_id: str
    metric_id: str
    condition: str  # "greater_than", "less_than", "equals", "change_rate"
    threshold: float
    severity: str  # "info", "warning", "critical"
    message_template: str
    cooldown_minutes: int = 5
    enabled: bool = True

@dataclass
class DashboardAlert:
    """Dashboard alert."""
    alert_id: str
    rule_id: str
    metric_id: str
    severity: str
    message: str
    triggered_at: str
    acknowledged: bool = False
    resolved: bool = False

@dataclass
class DashboardConfig:
    """Dashboard configuration."""
    refresh_interval: int = 30  # seconds
    max_data_points: int = 1000
    enable_alerts: bool = True
    enable_auto_refresh: bool = True
    theme: str = "dark"
    layout: str = "grid"

class RealTimeDashboard:
    """
    Real-time monitoring dashboard system.

    Provides comprehensive real-time monitoring including:
    - Live transaction monitoring
    - Performance metrics tracking
    - Alert management and notifications
    - Dynamic chart updates
    - Investigation progress tracking
    """

    def __init__(self, config: InvestigationConfig):
        """
        Initialize real-time dashboard.

        Args:
            config: Investigation configuration
        """
        self.config = config
        self.dashboard_config = DashboardConfig()
        self.metrics = {}
        self.alert_rules = {}
        self.active_alerts = {}
        self.data_streams = {}
        self.performance_monitor = PerformanceMonitor()
        self.update_callbacks = []

        # Initialize default metrics and alerts
        self._initialize_default_metrics()
        self._initialize_default_alerts()

        logger.info("Real-time dashboard initialized")

    def _initialize_default_metrics(self) -> None:
        """Initialize default dashboard metrics."""
        default_metrics = [
            DashboardMetric(
                metric_id="transaction_count",
                name="Total Transactions",
                value=0,
                unit="count",
                trend="stable",
                change_percent=0.0
            ),
            DashboardMetric(
                metric_id="total_volume",
                name="Total Volume",
                value=0.0,
                unit="BTC",
                trend="stable",
                change_percent=0.0
            ),
            DashboardMetric(
                metric_id="suspicious_activity_count",
                name="Suspicious Activities",
                value=0,
                unit="count",
                trend="stable",
                change_percent=0.0,
                threshold_warning=5.0,
                threshold_critical=10.0
            ),
            DashboardMetric(
                metric_id="processing_rate",
                name="Processing Rate",
                value=0.0,
                unit="tx/min",
                trend="stable",
                change_percent=0.0
            ),
            DashboardMetric(
                metric_id="memory_usage",
                name="Memory Usage",
                value=0.0,
                unit="MB",
                trend="stable",
                change_percent=0.0,
                threshold_warning=1000.0,
                threshold_critical=2000.0
            ),
            DashboardMetric(
                metric_id="api_response_time",
                name="API Response Time",
                value=0.0,
                unit="ms",
                trend="stable",
                change_percent=0.0,
                threshold_warning=1000.0,
                threshold_critical=5000.0
            )
        ]

        for metric in default_metrics:
            self.metrics[metric.metric_id] = metric

    def _initialize_default_alerts(self) -> None:
        """Initialize default alert rules."""
        default_alerts = [
            AlertRule(
                rule_id="high_suspicious_activity",
                metric_id="suspicious_activity_count",
                condition="greater_than",
                threshold=10.0,
                severity="critical",
                message_template="High suspicious activity detected: {value} activities",
                cooldown_minutes=10
            ),
            AlertRule(
                rule_id="memory_warning",
                metric_id="memory_usage",
                condition="greater_than",
                threshold=1000.0,
                severity="warning",
                message_template="High memory usage: {value} MB",
                cooldown_minutes=5
            ),
            AlertRule(
                rule_id="slow_api_response",
                metric_id="api_response_time",
                condition="greater_than",
                threshold=5000.0,
                severity="warning",
                message_template="Slow API response time: {value} ms",
                cooldown_minutes=3
            ),
            AlertRule(
                rule_id="large_transaction_volume",
                metric_id="total_volume",
                condition="change_rate",
                threshold=50.0,  # 50% increase
                severity="info",
                message_template="Large volume increase: {change_percent}% change",
                cooldown_minutes=15
            )
        ]

        for alert in default_alerts:
            self.alert_rules[alert.rule_id] = alert

    @performance_monitor("dashboard_update")
    async def update_metrics_async(self, investigation_data: Dict[str, Any]) -> None:
        """
        Update dashboard metrics with new investigation data.

        Args:
            investigation_data: Current investigation data
        """
        try:
            logger.debug("Updating dashboard metrics")

            # Update transaction metrics
            transactions = investigation_data.get("transactions", [])
            await self._update_transaction_metrics(transactions)

            # Update performance metrics
            await self._update_performance_metrics()

            # Update analysis metrics
            analysis_results = investigation_data.get("analysis_results", {})
            await self._update_analysis_metrics(analysis_results)

            # Check alert conditions
            if self.dashboard_config.enable_alerts:
                await self._check_alert_conditions()

            # Notify update callbacks
            await self._notify_update_callbacks()

            logger.debug("Dashboard metrics updated successfully")

        except Exception as e:
            logger.error(f"Failed to update dashboard metrics: {e}")
            raise VisualizationError(f"Dashboard update failed: {e}")

    async def _update_transaction_metrics(self, transactions: List[Dict[str, Any]]) -> None:
        """Update transaction-related metrics."""
        # Transaction count
        old_count = self.metrics["transaction_count"].value
        new_count = len(transactions)
        self._update_metric("transaction_count", new_count, old_count)

        # Total volume
        old_volume = self.metrics["total_volume"].value
        new_volume = sum(tx.get("amount_btc", 0) for tx in transactions)
        self._update_metric("total_volume", new_volume, old_volume)

        # Processing rate (transactions per minute)
        if len(transactions) > 1:
            # Calculate based on time span
            timestamps = []
            for tx in transactions:
                ts = TimeUtils.parse_timestamp(tx.get("timestamp", ""))
                if ts:
                    timestamps.append(ts)

            if len(timestamps) > 1:
                time_span = (max(timestamps) - min(timestamps)).total_seconds() / 60
                processing_rate = len(transactions) / max(time_span, 1)
                old_rate = self.metrics["processing_rate"].value
                self._update_metric("processing_rate", processing_rate, old_rate)

    async def _update_performance_metrics(self) -> None:
        """Update performance-related metrics."""
        # Get current memory usage
        memory_stats = self.performance_monitor.get_current_memory_usage()
        if memory_stats:
            old_memory = self.metrics["memory_usage"].value
            new_memory = memory_stats.get("process_memory_mb", 0)
            self._update_metric("memory_usage", new_memory, old_memory)

        # Get API response time (simulated for now)
        # In real implementation, this would track actual API response times
        old_response_time = self.metrics["api_response_time"].value
        new_response_time = 250.0  # Simulated 250ms response time
        self._update_metric("api_response_time", new_response_time, old_response_time)

    async def _update_analysis_metrics(self, analysis_results: Dict[str, Any]) -> None:
        """Update analysis-related metrics."""
        # Suspicious activity count
        suspicious_activities = analysis_results.get("suspicious_activities", [])
        old_suspicious = self.metrics["suspicious_activity_count"].value
        new_suspicious = len(suspicious_activities)
        self._update_metric("suspicious_activity_count", new_suspicious, old_suspicious)

    def _update_metric(self, metric_id: str, new_value: float, old_value: float) -> None:
        """Update a specific metric with trend calculation."""
        if metric_id not in self.metrics:
            return

        metric = self.metrics[metric_id]

        # Calculate change percentage
        if old_value != 0:
            change_percent = ((new_value - old_value) / old_value) * 100
        else:
            change_percent = 0.0 if new_value == 0 else 100.0

        # Determine trend
        if abs(change_percent) < 1.0:
            trend = "stable"
        elif change_percent > 0:
            trend = "up"
        else:
            trend = "down"

        # Update metric
        metric.value = new_value
        metric.change_percent = change_percent
        metric.trend = trend
        metric.last_updated = datetime.now().isoformat()

    async def _check_alert_conditions(self) -> None:
        """Check all alert conditions and trigger alerts if necessary."""
        for rule in self.alert_rules.values():
            if not rule.enabled:
                continue

            metric = self.metrics.get(rule.metric_id)
            if not metric:
                continue

            # Check if alert should be triggered
            should_trigger = self._evaluate_alert_condition(rule, metric)

            if should_trigger:
                await self._trigger_alert(rule, metric)

    def _evaluate_alert_condition(self, rule: AlertRule, metric: DashboardMetric) -> bool:
        """Evaluate if an alert condition is met."""
        if rule.condition == "greater_than":
            return metric.value > rule.threshold
        elif rule.condition == "less_than":
            return metric.value < rule.threshold
        elif rule.condition == "equals":
            return abs(metric.value - rule.threshold) < 0.001
        elif rule.condition == "change_rate":
            return abs(metric.change_percent) > rule.threshold

        return False

    async def _trigger_alert(self, rule: AlertRule, metric: DashboardMetric) -> None:
        """Trigger an alert."""
        # Check cooldown period
        if rule.rule_id in self.active_alerts:
            last_alert = self.active_alerts[rule.rule_id]
            last_time = TimeUtils.parse_timestamp(last_alert.triggered_at)
            if last_time:
                cooldown_end = last_time + timedelta(minutes=rule.cooldown_minutes)
                if datetime.now() < cooldown_end:
                    return  # Still in cooldown period

        # Create alert
        alert_id = f"alert_{rule.rule_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        message = rule.message_template.format(
            value=metric.value,
            change_percent=metric.change_percent,
            metric_name=metric.name
        )

        alert = DashboardAlert(
            alert_id=alert_id,
            rule_id=rule.rule_id,
            metric_id=rule.metric_id,
            severity=rule.severity,
            message=message,
            triggered_at=datetime.now().isoformat()
        )

        self.active_alerts[rule.rule_id] = alert

        logger.warning(f"Alert triggered: {message}")

        # Notify alert callbacks
        await self._notify_alert_callbacks(alert)

    async def _notify_update_callbacks(self) -> None:
        """Notify all registered update callbacks."""
        for callback in self.update_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(self.metrics)
                else:
                    callback(self.metrics)
            except Exception as e:
                logger.error(f"Update callback failed: {e}")

    async def _notify_alert_callbacks(self, alert: DashboardAlert) -> None:
        """Notify alert callbacks."""
        # This would integrate with notification systems
        logger.info(f"Alert notification: {alert.message}")

    def register_update_callback(self, callback: Callable) -> None:
        """Register a callback for metric updates."""
        self.update_callbacks.append(callback)

    @performance_monitor("dashboard_generation")
    async def generate_dashboard_async(self) -> go.Figure:
        """
        Generate complete dashboard visualization.

        Returns:
            Plotly figure with dashboard layout
        """
        try:
            logger.info("Generating real-time dashboard")

            # Create subplot layout
            fig = make_subplots(
                rows=3, cols=3,
                subplot_titles=[
                    "Transaction Volume", "Processing Rate", "Memory Usage",
                    "Suspicious Activities", "API Response Time", "Transaction Count",
                    "Alert Status", "Performance Trends", "Investigation Progress"
                ],
                specs=[
                    [{"type": "scatter"}, {"type": "scatter"}, {"type": "scatter"}],
                    [{"type": "bar"}, {"type": "scatter"}, {"type": "indicator"}],
                    [{"type": "table"}, {"type": "scatter"}, {"type": "pie"}]
                ]
            )

            # Add metric visualizations
            await self._add_volume_chart(fig, 1, 1)
            await self._add_processing_rate_chart(fig, 1, 2)
            await self._add_memory_usage_chart(fig, 1, 3)
            await self._add_suspicious_activity_chart(fig, 2, 1)
            await self._add_response_time_chart(fig, 2, 2)
            await self._add_transaction_count_indicator(fig, 2, 3)
            await self._add_alert_table(fig, 3, 1)
            await self._add_performance_trends(fig, 3, 2)
            await self._add_progress_pie(fig, 3, 3)

            # Update layout
            fig.update_layout(
                title="CryptoForensics Real-Time Dashboard",
                height=900,
                showlegend=False,
                template="plotly_dark" if self.dashboard_config.theme == "dark" else "plotly_white"
            )

            logger.info("Dashboard generated successfully")
            return fig

        except Exception as e:
            logger.error(f"Dashboard generation failed: {e}")
            raise VisualizationError(f"Dashboard generation failed: {e}")

    async def _add_volume_chart(self, fig: go.Figure, row: int, col: int) -> None:
        """Add transaction volume chart."""
        # Simulated time series data
        times = [datetime.now() - timedelta(minutes=i*5) for i in range(20, 0, -1)]
        volumes = [self.metrics["total_volume"].value * (0.8 + 0.4 * i / 20) for i in range(20)]

        fig.add_trace(
            go.Scatter(
                x=times,
                y=volumes,
                mode='lines+markers',
                name='Volume',
                line=dict(color='#00ff00', width=2)
            ),
            row=row, col=col
        )

    async def _add_processing_rate_chart(self, fig: go.Figure, row: int, col: int) -> None:
        """Add processing rate chart."""
        # Simulated processing rate data
        times = [datetime.now() - timedelta(minutes=i*5) for i in range(20, 0, -1)]
        rates = [self.metrics["processing_rate"].value * (0.9 + 0.2 * i / 20) for i in range(20)]

        fig.add_trace(
            go.Scatter(
                x=times,
                y=rates,
                mode='lines+markers',
                name='Processing Rate',
                line=dict(color='#ff6600', width=2)
            ),
            row=row, col=col
        )

    async def _add_memory_usage_chart(self, fig: go.Figure, row: int, col: int) -> None:
        """Add memory usage chart."""
        # Simulated memory usage data
        times = [datetime.now() - timedelta(minutes=i*5) for i in range(20, 0, -1)]
        memory = [self.metrics["memory_usage"].value * (0.7 + 0.6 * i / 20) for i in range(20)]

        fig.add_trace(
            go.Scatter(
                x=times,
                y=memory,
                mode='lines+markers',
                name='Memory Usage',
                line=dict(color='#ff0066', width=2),
                fill='tonexty'
            ),
            row=row, col=col
        )

    async def _add_suspicious_activity_chart(self, fig: go.Figure, row: int, col: int) -> None:
        """Add suspicious activity bar chart."""
        categories = ['Mixing', 'Large Transfers', 'Rapid Succession', 'Unusual Patterns']
        counts = [3, 7, 2, 1]  # Simulated data

        fig.add_trace(
            go.Bar(
                x=categories,
                y=counts,
                name='Suspicious Activities',
                marker_color=['#ff4444', '#ff6666', '#ff8888', '#ffaaaa']
            ),
            row=row, col=col
        )

    async def _add_response_time_chart(self, fig: go.Figure, row: int, col: int) -> None:
        """Add API response time chart."""
        # Simulated response time data
        times = [datetime.now() - timedelta(minutes=i*2) for i in range(30, 0, -1)]
        response_times = [self.metrics["api_response_time"].value * (0.5 + 1.0 * i / 30) for i in range(30)]

        fig.add_trace(
            go.Scatter(
                x=times,
                y=response_times,
                mode='lines',
                name='Response Time',
                line=dict(color='#00ccff', width=1)
            ),
            row=row, col=col
        )

    async def _add_transaction_count_indicator(self, fig: go.Figure, row: int, col: int) -> None:
        """Add transaction count indicator."""
        fig.add_trace(
            go.Indicator(
                mode="gauge+number+delta",
                value=self.metrics["transaction_count"].value,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "Transactions"},
                delta={'reference': self.metrics["transaction_count"].value * 0.9},
                gauge={
                    'axis': {'range': [None, self.metrics["transaction_count"].value * 1.5]},
                    'bar': {'color': "darkblue"},
                    'steps': [
                        {'range': [0, self.metrics["transaction_count"].value * 0.5], 'color': "lightgray"},
                        {'range': [self.metrics["transaction_count"].value * 0.5, self.metrics["transaction_count"].value], 'color': "gray"}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': self.metrics["transaction_count"].value * 1.2
                    }
                }
            ),
            row=row, col=col
        )

    async def _add_alert_table(self, fig: go.Figure, row: int, col: int) -> None:
        """Add alert status table."""
        # Get recent alerts
        alerts = list(self.active_alerts.values())[-5:]  # Last 5 alerts

        if alerts:
            alert_data = {
                'Severity': [alert.severity for alert in alerts],
                'Message': [alert.message[:50] + '...' if len(alert.message) > 50 else alert.message for alert in alerts],
                'Time': [alert.triggered_at.split('T')[1][:8] for alert in alerts]
            }
        else:
            alert_data = {
                'Severity': ['No alerts'],
                'Message': ['System operating normally'],
                'Time': [datetime.now().strftime('%H:%M:%S')]
            }

        fig.add_trace(
            go.Table(
                header=dict(values=list(alert_data.keys()),
                           fill_color='paleturquoise',
                           align='left'),
                cells=dict(values=list(alert_data.values()),
                          fill_color='lavender',
                          align='left')
            ),
            row=row, col=col
        )

    async def _add_performance_trends(self, fig: go.Figure, row: int, col: int) -> None:
        """Add performance trends chart."""
        # Simulated performance trend data
        times = [datetime.now() - timedelta(hours=i) for i in range(24, 0, -1)]
        cpu_usage = [50 + 30 * np.sin(i * 0.1) for i in range(24)]

        fig.add_trace(
            go.Scatter(
                x=times,
                y=cpu_usage,
                mode='lines',
                name='CPU Usage %',
                line=dict(color='#ffcc00', width=2)
            ),
            row=row, col=col
        )

    async def _add_progress_pie(self, fig: go.Figure, row: int, col: int) -> None:
        """Add investigation progress pie chart."""
        labels = ['Completed', 'In Progress', 'Pending']
        values = [60, 25, 15]  # Simulated progress percentages

        fig.add_trace(
            go.Pie(
                labels=labels,
                values=values,
                hole=0.3,
                marker_colors=['#00ff00', '#ffaa00', '#ff0000']
            ),
            row=row, col=col
        )

    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of current metrics."""
        return {
            metric_id: {
                'value': metric.value,
                'unit': metric.unit,
                'trend': metric.trend,
                'change_percent': metric.change_percent,
                'last_updated': metric.last_updated
            }
            for metric_id, metric in self.metrics.items()
        }

    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get list of active alerts."""
        return [
            {
                'alert_id': alert.alert_id,
                'severity': alert.severity,
                'message': alert.message,
                'triggered_at': alert.triggered_at,
                'acknowledged': alert.acknowledged
            }
            for alert in self.active_alerts.values()
            if not alert.resolved
        ]

    def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge an alert."""
        for alert in self.active_alerts.values():
            if alert.alert_id == alert_id:
                alert.acknowledged = True
                logger.info(f"Alert acknowledged: {alert_id}")
                return True
        return False
