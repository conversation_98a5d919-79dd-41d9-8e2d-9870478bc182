"""
Data models for CryptoForensics

Contains data classes and models used throughout the package.
"""

import networkx as nx
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any

@dataclass
class TransactionInfo:
    """Enhanced transaction information with additional metadata."""
    txid: str
    from_address: str
    to_address: str
    amount_btc: float
    depth: int
    timestamp: str
    block_height: Optional[int] = None
    confirmations: bool = False
    investigation_id: str = ""
    network: str = "bitcoin"
    fee: Optional[float] = None
    input_count: int = 0
    output_count: int = 0
    is_coinbase: bool = False
    confidence_score: float = 1.0
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []

@dataclass
class InvestigationResult:
    """Comprehensive investigation result container."""
    investigation_id: str
    timestamp: str
    input_parameters: Dict[str, Any]
    basic_results: Dict[str, Any]
    graph: nx.DiGraph
    detailed_transactions: List[TransactionInfo]
    advanced_analysis: Dict[str, Any]
    investigation_summary: Dict[str, Any]
    evidence_items: List[Any]
    audit_trail: List[Any]
    performance_metrics: Dict[str, Any]
    quality_assessment: Dict[str, Any]
