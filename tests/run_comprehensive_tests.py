"""
Comprehensive test runner for CryptoForensics v3.0

Runs all tests for the enhanced features and generates a comprehensive report.
"""

import pytest
import sys
import os
import time
from pathlib import Path
import subprocess
import json
from datetime import datetime

def run_test_suite():
    """Run comprehensive test suite for all new features."""
    
    print("=" * 80)
    print("CryptoForensics v3.0 - Comprehensive Test Suite")
    print("=" * 80)
    print(f"Started at: {datetime.now().isoformat()}")
    print()
    
    # Test configuration
    test_config = {
        "verbose": True,
        "capture": "no",  # Show print statements
        "tb": "short",    # Short traceback format
        "maxfail": 5,     # Stop after 5 failures
        "timeout": 300,   # 5 minute timeout per test
    }
    
    # Test modules to run
    test_modules = [
        "test_enhanced_visualization.py",
        "test_security_features.py", 
        "test_investigation_features.py",
        "test_core_functionality.py",
        "test_analysis_modules.py",
        "test_evidence_collection.py"
    ]
    
    # Results tracking
    results = {
        "total_tests": 0,
        "passed": 0,
        "failed": 0,
        "skipped": 0,
        "errors": 0,
        "duration": 0,
        "modules": {}
    }
    
    start_time = time.time()
    
    # Run each test module
    for module in test_modules:
        module_path = Path(__file__).parent / module
        
        if not module_path.exists():
            print(f"⚠️  Test module not found: {module}")
            continue
        
        print(f"🧪 Running tests in {module}...")
        print("-" * 60)
        
        module_start = time.time()
        
        # Run pytest for this module
        cmd = [
            sys.executable, "-m", "pytest",
            str(module_path),
            "-v",
            "--tb=short",
            "--maxfail=5",
            "--timeout=300",
            "--json-report",
            f"--json-report-file=test_results_{module.replace('.py', '.json')}"
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600  # 10 minute timeout for entire module
            )
            
            module_duration = time.time() - module_start
            
            # Parse results
            if result.returncode == 0:
                print(f"✅ {module} - All tests passed ({module_duration:.2f}s)")
                status = "PASSED"
            elif result.returncode == 1:
                print(f"❌ {module} - Some tests failed ({module_duration:.2f}s)")
                status = "FAILED"
            else:
                print(f"💥 {module} - Test execution error ({module_duration:.2f}s)")
                status = "ERROR"
            
            # Show output if there were issues
            if result.returncode != 0:
                print("STDOUT:")
                print(result.stdout)
                print("STDERR:")
                print(result.stderr)
            
            # Try to load JSON report
            json_file = Path(f"test_results_{module.replace('.py', '.json')}")
            if json_file.exists():
                try:
                    with open(json_file) as f:
                        module_results = json.load(f)
                    
                    summary = module_results.get("summary", {})
                    results["modules"][module] = {
                        "status": status,
                        "duration": module_duration,
                        "total": summary.get("total", 0),
                        "passed": summary.get("passed", 0),
                        "failed": summary.get("failed", 0),
                        "skipped": summary.get("skipped", 0),
                        "error": summary.get("error", 0)
                    }
                    
                    # Update totals
                    results["total_tests"] += summary.get("total", 0)
                    results["passed"] += summary.get("passed", 0)
                    results["failed"] += summary.get("failed", 0)
                    results["skipped"] += summary.get("skipped", 0)
                    results["errors"] += summary.get("error", 0)
                    
                except Exception as e:
                    print(f"⚠️  Could not parse JSON report: {e}")
                    results["modules"][module] = {
                        "status": status,
                        "duration": module_duration,
                        "error": str(e)
                    }
            else:
                results["modules"][module] = {
                    "status": status,
                    "duration": module_duration,
                    "note": "No JSON report generated"
                }
        
        except subprocess.TimeoutExpired:
            print(f"⏰ {module} - Tests timed out")
            results["modules"][module] = {
                "status": "TIMEOUT",
                "duration": 600,
                "error": "Test execution timed out"
            }
        
        except Exception as e:
            print(f"💥 {module} - Unexpected error: {e}")
            results["modules"][module] = {
                "status": "ERROR",
                "duration": 0,
                "error": str(e)
            }
        
        print()
    
    # Calculate total duration
    results["duration"] = time.time() - start_time
    
    # Generate summary report
    print("=" * 80)
    print("TEST SUMMARY REPORT")
    print("=" * 80)
    
    print(f"Total Duration: {results['duration']:.2f} seconds")
    print(f"Total Tests: {results['total_tests']}")
    print(f"Passed: {results['passed']} ✅")
    print(f"Failed: {results['failed']} ❌")
    print(f"Skipped: {results['skipped']} ⏭️")
    print(f"Errors: {results['errors']} 💥")
    
    if results['total_tests'] > 0:
        pass_rate = (results['passed'] / results['total_tests']) * 100
        print(f"Pass Rate: {pass_rate:.1f}%")
    
    print()
    print("MODULE BREAKDOWN:")
    print("-" * 40)
    
    for module, module_results in results["modules"].items():
        status_icon = {
            "PASSED": "✅",
            "FAILED": "❌", 
            "ERROR": "💥",
            "TIMEOUT": "⏰"
        }.get(module_results["status"], "❓")
        
        duration = module_results.get("duration", 0)
        print(f"{status_icon} {module:<35} ({duration:.2f}s)")
        
        if "total" in module_results:
            total = module_results["total"]
            passed = module_results["passed"]
            failed = module_results["failed"]
            print(f"   Tests: {total}, Passed: {passed}, Failed: {failed}")
        
        if "error" in module_results:
            print(f"   Error: {module_results['error']}")
        
        print()
    
    # Feature coverage report
    print("FEATURE COVERAGE REPORT:")
    print("-" * 40)
    
    feature_coverage = {
        "Enhanced Visualization": {
            "3D Transaction Graphs": "test_enhanced_visualization.py",
            "Real-time Dashboard": "test_enhanced_visualization.py", 
            "Advanced Analytics": "test_enhanced_visualization.py"
        },
        "Security & Robustness": {
            "Enhanced Security": "test_security_features.py",
            "Fault Tolerance": "test_security_features.py",
            "Access Control": "test_security_features.py"
        },
        "Professional Investigation": {
            "Victim Recovery": "test_investigation_features.py",
            "Law Enforcement Integration": "test_investigation_features.py",
            "Case Management": "test_investigation_features.py"
        },
        "Core Functionality": {
            "Transaction Analysis": "test_core_functionality.py",
            "Address Validation": "test_core_functionality.py",
            "API Integration": "test_core_functionality.py"
        }
    }
    
    for feature_category, features in feature_coverage.items():
        print(f"\n{feature_category}:")
        for feature, test_file in features.items():
            if test_file in results["modules"]:
                status = results["modules"][test_file]["status"]
                icon = "✅" if status == "PASSED" else "❌"
                print(f"  {icon} {feature}")
            else:
                print(f"  ❓ {feature} (test file not found)")
    
    # Recommendations
    print("\nRECOMMENDations:")
    print("-" * 40)
    
    if results["failed"] > 0:
        print("❌ Some tests failed. Review failed tests and fix issues before deployment.")
    
    if results["errors"] > 0:
        print("💥 Test execution errors detected. Check test environment and dependencies.")
    
    if results["total_tests"] == 0:
        print("⚠️  No tests were executed. Verify test files exist and are properly configured.")
    
    if results["total_tests"] > 0:
        pass_rate = (results["passed"] / results["total_tests"]) * 100
        if pass_rate >= 95:
            print("🎉 Excellent test coverage and pass rate! Ready for deployment.")
        elif pass_rate >= 80:
            print("👍 Good test coverage. Consider addressing failing tests.")
        else:
            print("⚠️  Low pass rate. Significant issues need to be addressed.")
    
    # Save detailed results
    results_file = Path("comprehensive_test_results.json")
    with open(results_file, "w") as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📊 Detailed results saved to: {results_file}")
    
    # Return exit code
    if results["failed"] > 0 or results["errors"] > 0:
        return 1
    else:
        return 0


def check_test_environment():
    """Check if test environment is properly set up."""
    
    print("🔍 Checking test environment...")
    
    # Check Python version
    python_version = sys.version_info
    if python_version < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    else:
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Check required packages
    required_packages = [
        "pytest",
        "pytest-asyncio", 
        "pytest-timeout",
        "pytest-json-report"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} (missing)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    # Check test files exist
    test_files = [
        "test_enhanced_visualization.py",
        "test_security_features.py",
        "test_investigation_features.py"
    ]
    
    missing_files = []
    for test_file in test_files:
        if not (Path(__file__).parent / test_file).exists():
            missing_files.append(test_file)
    
    if missing_files:
        print(f"\n⚠️  Missing test files: {', '.join(missing_files)}")
        return False
    
    print("✅ Test environment ready")
    return True


if __name__ == "__main__":
    print("CryptoForensics v3.0 - Comprehensive Test Runner")
    print("=" * 50)
    
    # Check environment first
    if not check_test_environment():
        print("\n❌ Test environment check failed. Please fix issues and try again.")
        sys.exit(1)
    
    print()
    
    # Run tests
    exit_code = run_test_suite()
    
    print(f"\n🏁 Test run completed with exit code: {exit_code}")
    sys.exit(exit_code)
