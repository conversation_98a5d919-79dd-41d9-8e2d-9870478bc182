"""
Tests for enhanced visualization features.

Tests the advanced 3D visualization, real-time dashboard, and analytics modules.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from cryptoforensics.models import TransactionInfo
from cryptoforensics.core.config import InvestigationConfig
from cryptoforensics.visualization.advanced_3d import (
    Advanced3DVisualizer, Visualization3DConfig, Node3D, Edge3D
)
from cryptoforensics.visualization.realtime_dashboard import (
    RealTimeDashboard, DashboardConfig, DashboardMetric
)
from cryptoforensics.visualization.advanced_analytics import (
    AdvancedAnalytics, BehavioralPattern, AnomalyResult
)


class TestAdvanced3DVisualizer:
    """Test cases for Advanced3DVisualizer."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return InvestigationConfig(
            investigation_id="test_3d_viz",
            target_address="**********************************",
            max_depth=3,
            api_key="test_key"
        )
    
    @pytest.fixture
    def visualizer(self, config):
        """Create Advanced3DVisualizer instance."""
        return Advanced3DVisualizer(config)
    
    @pytest.fixture
    def sample_transactions(self):
        """Create sample transactions for testing."""
        return [
            TransactionInfo(
                txid="tx1",
                from_address="addr1",
                to_address="addr2",
                amount_btc=1.5,
                timestamp="2023-01-01T10:00:00Z",
                depth=1,
                confirmations=6
            ),
            TransactionInfo(
                txid="tx2",
                from_address="addr2",
                to_address="addr3",
                amount_btc=0.8,
                timestamp="2023-01-01T11:00:00Z",
                depth=2,
                confirmations=6
            ),
            TransactionInfo(
                txid="tx3",
                from_address="addr1",
                to_address="addr4",
                amount_btc=2.1,
                timestamp="2023-01-01T12:00:00Z",
                depth=1,
                confirmations=6
            )
        ]
    
    def test_visualizer_initialization(self, visualizer):
        """Test visualizer initialization."""
        assert visualizer is not None
        assert visualizer.config is not None
        assert visualizer.color_schemes is not None
        assert visualizer.layout_algorithms is not None
        assert len(visualizer.color_schemes) > 0
        assert len(visualizer.layout_algorithms) > 0
    
    @pytest.mark.asyncio
    async def test_3d_transaction_flow_creation(self, visualizer, sample_transactions):
        """Test 3D transaction flow visualization creation."""
        config = Visualization3DConfig(
            layout_algorithm="force_directed_3d",
            color_scheme="risk_based",
            animation_enabled=False
        )
        
        result = await visualizer.create_3d_transaction_flow_async(sample_transactions, config)
        
        assert result is not None
        assert "figure" in result
        assert "nodes" in result
        assert "edges" in result
        assert "metadata" in result
        assert len(result["nodes"]) > 0
        assert len(result["edges"]) > 0
        assert result["metadata"]["transaction_count"] == len(sample_transactions)
    
    def test_build_transaction_graph(self, visualizer, sample_transactions):
        """Test transaction graph building."""
        graph = visualizer._build_transaction_graph(sample_transactions)
        
        assert graph is not None
        assert graph.number_of_nodes() > 0
        assert graph.number_of_edges() > 0
        
        # Check that all addresses are in the graph
        addresses = set()
        for tx in sample_transactions:
            addresses.add(tx.from_address)
            addresses.add(tx.to_address)
        
        for addr in addresses:
            assert addr in graph.nodes()
    
    @pytest.mark.asyncio
    async def test_force_directed_layout(self, visualizer, sample_transactions):
        """Test force-directed 3D layout algorithm."""
        graph = visualizer._build_transaction_graph(sample_transactions)
        config = Visualization3DConfig()
        
        nodes_3d, edges_3d = await visualizer._force_directed_3d_layout(
            graph, sample_transactions, config
        )
        
        assert len(nodes_3d) > 0
        assert len(edges_3d) > 0
        
        # Check node properties
        for node in nodes_3d:
            assert isinstance(node, Node3D)
            assert node.id is not None
            assert isinstance(node.x, float)
            assert isinstance(node.y, float)
            assert isinstance(node.z, float)
            assert node.size > 0
            assert node.color is not None
        
        # Check edge properties
        for edge in edges_3d:
            assert isinstance(edge, Edge3D)
            assert edge.source is not None
            assert edge.target is not None
            assert edge.weight >= 0
            assert edge.color is not None
    
    @pytest.mark.asyncio
    async def test_temporal_layout(self, visualizer, sample_transactions):
        """Test temporal 3D layout algorithm."""
        graph = visualizer._build_transaction_graph(sample_transactions)
        config = Visualization3DConfig(layout_algorithm="temporal_3d")
        
        nodes_3d, edges_3d = await visualizer._temporal_3d_layout(
            graph, sample_transactions, config
        )
        
        assert len(nodes_3d) > 0
        assert len(edges_3d) > 0
        
        # Check that z-coordinates represent temporal ordering
        node_times = []
        for node in nodes_3d:
            if node.metadata.get("earliest_transaction"):
                node_times.append((node.z, node.metadata["earliest_transaction"]))
        
        # Should have some temporal data
        assert len(node_times) > 0
    
    def test_color_calculation(self, visualizer):
        """Test node and edge color calculation."""
        # Test node color calculation
        node_data = {"total_sent": 50, "total_received": 25}
        color = visualizer._calculate_node_color("test_addr", node_data, "risk_based")
        assert color is not None
        assert isinstance(color, str)
        
        # Test edge color calculation
        edge_data = {"weight": 10.5, "transaction_count": 3}
        color = visualizer._calculate_edge_color(edge_data, "risk_based")
        assert color is not None
        assert isinstance(color, str)


class TestRealTimeDashboard:
    """Test cases for RealTimeDashboard."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return InvestigationConfig(
            investigation_id="test_dashboard",
            target_address="**********************************",
            max_depth=3,
            api_key="test_key"
        )
    
    @pytest.fixture
    def dashboard(self, config):
        """Create RealTimeDashboard instance."""
        return RealTimeDashboard(config)
    
    def test_dashboard_initialization(self, dashboard):
        """Test dashboard initialization."""
        assert dashboard is not None
        assert dashboard.config is not None
        assert dashboard.metrics is not None
        assert dashboard.alert_rules is not None
        assert len(dashboard.metrics) > 0
        assert len(dashboard.alert_rules) > 0
    
    def test_default_metrics_initialization(self, dashboard):
        """Test default metrics initialization."""
        expected_metrics = [
            "transaction_count", "total_volume", "suspicious_activity_count",
            "processing_rate", "memory_usage", "api_response_time"
        ]
        
        for metric_id in expected_metrics:
            assert metric_id in dashboard.metrics
            metric = dashboard.metrics[metric_id]
            assert isinstance(metric, DashboardMetric)
            assert metric.metric_id == metric_id
            assert metric.name is not None
            assert metric.unit is not None
    
    @pytest.mark.asyncio
    async def test_metrics_update(self, dashboard):
        """Test metrics update functionality."""
        investigation_data = {
            "transactions": [
                {"amount_btc": 1.5, "timestamp": "2023-01-01T10:00:00Z"},
                {"amount_btc": 2.3, "timestamp": "2023-01-01T11:00:00Z"}
            ],
            "analysis_results": {
                "suspicious_activities": [
                    {"type": "large_transaction", "confidence": 0.8}
                ]
            }
        }
        
        await dashboard.update_metrics_async(investigation_data)
        
        # Check that metrics were updated
        assert dashboard.metrics["transaction_count"].value == 2
        assert dashboard.metrics["total_volume"].value == 3.8
        assert dashboard.metrics["suspicious_activity_count"].value == 1
    
    def test_metric_update_with_trend(self, dashboard):
        """Test metric update with trend calculation."""
        metric_id = "transaction_count"
        old_value = 10
        new_value = 15
        
        dashboard._update_metric(metric_id, new_value, old_value)
        
        metric = dashboard.metrics[metric_id]
        assert metric.value == new_value
        assert metric.change_percent == 50.0  # 50% increase
        assert metric.trend == "up"
    
    @pytest.mark.asyncio
    async def test_alert_condition_evaluation(self, dashboard):
        """Test alert condition evaluation."""
        # Set up a metric that should trigger an alert
        dashboard.metrics["suspicious_activity_count"].value = 15
        
        await dashboard._check_alert_conditions()
        
        # Check if alert was triggered
        rule_id = "high_suspicious_activity"
        assert rule_id in dashboard.active_alerts
        alert = dashboard.active_alerts[rule_id]
        assert alert.severity == "critical"
    
    @pytest.mark.asyncio
    async def test_dashboard_generation(self, dashboard):
        """Test dashboard figure generation."""
        fig = await dashboard.generate_dashboard_async()
        
        assert fig is not None
        # Check that figure has the expected structure
        assert hasattr(fig, 'data')
        assert hasattr(fig, 'layout')
        assert fig.layout.title.text == "CryptoForensics Real-Time Dashboard"
    
    def test_security_status(self, dashboard):
        """Test security status retrieval."""
        status = dashboard.get_security_status()
        
        assert status is not None
        assert "active_sessions" in status
        assert "recent_alerts" in status
        assert "failed_attempts_last_hour" in status
        assert "security_level" in status


class TestAdvancedAnalytics:
    """Test cases for AdvancedAnalytics."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return InvestigationConfig(
            investigation_id="test_analytics",
            target_address="**********************************",
            max_depth=3,
            api_key="test_key"
        )
    
    @pytest.fixture
    def analytics(self, config):
        """Create AdvancedAnalytics instance."""
        return AdvancedAnalytics(config)
    
    @pytest.fixture
    def sample_transactions(self):
        """Create sample transactions for testing."""
        transactions = []
        base_time = datetime.now()
        
        for i in range(20):
            tx = TransactionInfo(
                txid=f"tx{i}",
                from_address=f"addr{i % 5}",  # Create some address reuse
                to_address=f"addr{(i + 1) % 5}",
                amount_btc=1.0 + (i % 3) * 0.5,  # Varying amounts
                timestamp=(base_time + timedelta(hours=i)).isoformat(),
                depth=1,
                confirmations=6
            )
            transactions.append(tx)
        
        return transactions
    
    def test_analytics_initialization(self, analytics):
        """Test analytics initialization."""
        assert analytics is not None
        assert analytics.config is not None
        assert analytics.models is not None
        assert "anomaly_detector" in analytics.models
        assert "clustering" in analytics.models
    
    @pytest.mark.asyncio
    async def test_behavioral_pattern_detection(self, analytics, sample_transactions):
        """Test behavioral pattern detection."""
        patterns = await analytics.detect_behavioral_patterns_async(sample_transactions)
        
        assert isinstance(patterns, list)
        # Should detect some patterns with the sample data
        assert len(patterns) >= 0
        
        for pattern in patterns:
            assert isinstance(pattern, BehavioralPattern)
            assert pattern.pattern_id is not None
            assert pattern.pattern_type is not None
            assert 0 <= pattern.confidence <= 1
            assert 0 <= pattern.risk_score <= 1
    
    @pytest.mark.asyncio
    async def test_feature_extraction(self, analytics, sample_transactions):
        """Test behavioral feature extraction."""
        features_df = await analytics._extract_behavioral_features(sample_transactions)
        
        assert features_df is not None
        if not features_df.empty:
            expected_columns = [
                'address', 'tx_count', 'amount_mean', 'amount_std',
                'interval_mean', 'destination_diversity'
            ]
            
            for col in expected_columns:
                assert col in features_df.columns
            
            # Check data types and ranges
            assert features_df['tx_count'].min() >= 1
            assert features_df['amount_mean'].min() >= 0
            assert 0 <= features_df['destination_diversity'].max() <= 1
    
    def test_entropy_calculation(self, analytics):
        """Test entropy calculation."""
        # Test with uniform distribution
        uniform_values = [1, 1, 1, 1]
        entropy = analytics._calculate_entropy(uniform_values)
        assert entropy == 0.0  # No entropy in uniform distribution
        
        # Test with varied distribution
        varied_values = [1, 2, 3, 4, 1, 2, 3, 4]
        entropy = analytics._calculate_entropy(varied_values)
        assert entropy > 0  # Should have some entropy
    
    @pytest.mark.asyncio
    async def test_anomaly_detection(self, analytics, sample_transactions):
        """Test anomaly detection."""
        anomalies = await analytics.detect_anomalies_async(sample_transactions)
        
        assert isinstance(anomalies, list)
        
        for anomaly in anomalies:
            assert isinstance(anomaly, AnomalyResult)
            assert anomaly.anomaly_id is not None
            assert anomaly.anomaly_type is not None
            assert anomaly.severity in ["low", "medium", "high", "critical"]
            assert 0 <= anomaly.confidence <= 1
    
    @pytest.mark.asyncio
    async def test_temporal_pattern_analysis(self, analytics, sample_transactions):
        """Test temporal pattern analysis."""
        patterns = await analytics._analyze_temporal_patterns(sample_transactions)
        
        assert isinstance(patterns, list)
        
        for pattern in patterns:
            assert isinstance(pattern, BehavioralPattern)
            assert pattern.pattern_type == "temporal_clustering"
            assert "peak_hour" in pattern.characteristics
    
    @pytest.mark.asyncio
    async def test_amount_pattern_analysis(self, analytics, sample_transactions):
        """Test amount pattern analysis."""
        # Create transactions with round amounts
        round_amount_txs = []
        for i in range(10):
            tx = TransactionInfo(
                txid=f"round_tx{i}",
                from_address=f"addr{i}",
                to_address=f"addr{i+10}",
                amount_btc=float(i + 1),  # Round amounts: 1.0, 2.0, 3.0, etc.
                timestamp=datetime.now().isoformat(),
                depth=1,
                confirmations=6
            )
            round_amount_txs.append(tx)
        
        patterns = await analytics._analyze_amount_patterns(round_amount_txs)
        
        assert isinstance(patterns, list)
        # Should detect round amount pattern
        if patterns:
            pattern = patterns[0]
            assert pattern.pattern_type == "round_amounts"
            assert "round_amount_ratio" in pattern.characteristics
    
    @pytest.mark.asyncio
    async def test_network_pattern_analysis(self, analytics, sample_transactions):
        """Test network pattern analysis."""
        patterns = await analytics._analyze_network_patterns(sample_transactions)
        
        assert isinstance(patterns, list)
        
        for pattern in patterns:
            assert isinstance(pattern, BehavioralPattern)
            assert pattern.pattern_type == "network_hubs"
            assert "hub_count" in pattern.characteristics


@pytest.mark.integration
class TestVisualizationIntegration:
    """Integration tests for visualization components."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return InvestigationConfig(
            investigation_id="test_integration",
            target_address="**********************************",
            max_depth=3,
            api_key="test_key"
        )
    
    @pytest.mark.asyncio
    async def test_end_to_end_visualization_pipeline(self, config):
        """Test complete visualization pipeline."""
        # Create sample data
        transactions = [
            TransactionInfo(
                txid="integration_tx1",
                from_address="addr1",
                to_address="addr2",
                amount_btc=5.0,
                timestamp="2023-01-01T10:00:00Z",
                depth=1,
                confirmations=6
            ),
            TransactionInfo(
                txid="integration_tx2",
                from_address="addr2",
                to_address="addr3",
                amount_btc=3.2,
                timestamp="2023-01-01T11:00:00Z",
                depth=2,
                confirmations=6
            )
        ]
        
        # Test 3D visualization
        visualizer = Advanced3DVisualizer(config)
        viz_config = Visualization3DConfig()
        viz_result = await visualizer.create_3d_transaction_flow_async(transactions, viz_config)
        assert viz_result is not None
        
        # Test analytics
        analytics = AdvancedAnalytics(config)
        patterns = await analytics.detect_behavioral_patterns_async(transactions)
        anomalies = await analytics.detect_anomalies_async(transactions)
        
        # Test dashboard with analytics results
        dashboard = RealTimeDashboard(config)
        investigation_data = {
            "transactions": [tx.__dict__ for tx in transactions],
            "analysis_results": {
                "patterns": [p.__dict__ for p in patterns],
                "anomalies": [a.__dict__ for a in anomalies],
                "suspicious_activities": []
            }
        }
        
        await dashboard.update_metrics_async(investigation_data)
        dashboard_fig = await dashboard.generate_dashboard_async()
        
        assert dashboard_fig is not None
        assert len(dashboard.metrics) > 0
    
    @pytest.mark.asyncio
    async def test_performance_under_load(self, config):
        """Test visualization performance with larger datasets."""
        # Create larger dataset
        transactions = []
        for i in range(100):
            tx = TransactionInfo(
                txid=f"perf_tx{i}",
                from_address=f"addr{i % 20}",
                to_address=f"addr{(i + 1) % 20}",
                amount_btc=1.0 + (i % 10) * 0.1,
                timestamp=datetime.now().isoformat(),
                depth=i % 5 + 1,
                confirmations=6
            )
            transactions.append(tx)
        
        # Test 3D visualization performance
        visualizer = Advanced3DVisualizer(config)
        viz_config = Visualization3DConfig()
        
        import time
        start_time = time.time()
        result = await visualizer.create_3d_transaction_flow_async(transactions, viz_config)
        end_time = time.time()
        
        assert result is not None
        assert (end_time - start_time) < 10.0  # Should complete within 10 seconds
        
        # Test analytics performance
        analytics = AdvancedAnalytics(config)
        
        start_time = time.time()
        patterns = await analytics.detect_behavioral_patterns_async(transactions)
        anomalies = await analytics.detect_anomalies_async(transactions)
        end_time = time.time()
        
        assert isinstance(patterns, list)
        assert isinstance(anomalies, list)
        assert (end_time - start_time) < 15.0  # Should complete within 15 seconds


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
