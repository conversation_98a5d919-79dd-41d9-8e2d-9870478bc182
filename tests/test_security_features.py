"""
Tests for enhanced security features.

Tests the enhanced security system and fault tolerance manager.
"""

import pytest
import asyncio
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from cryptoforensics.core.config import InvestigationConfig
from cryptoforensics.security.enhanced_security import (
    EnhancedSecurity, SecurityContext, AccessAttempt, SecurityAlert
)
from cryptoforensics.security.fault_tolerance import (
    FaultToleranceManager, SystemState, FailureEvent, RecoveryAction
)
from cryptoforensics.exceptions import SecurityError, SystemError


class TestEnhancedSecurity:
    """Test cases for EnhancedSecurity."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return InvestigationConfig(
            investigation_id="test_security",
            target_address="**********************************",
            max_depth=3,
            api_key="test_key"
        )
    
    @pytest.fixture
    def security(self, config):
        """Create EnhancedSecurity instance."""
        return EnhancedSecurity(config)
    
    def test_security_initialization(self, security):
        """Test security system initialization."""
        assert security is not None
        assert security.config is not None
        assert security.active_sessions is not None
        assert security.permissions is not None
        assert security.access_levels is not None
        assert len(security.permissions) > 0
        assert len(security.access_levels) > 0
    
    def test_key_initialization(self, security):
        """Test security key initialization."""
        assert security.master_key is not None
        assert len(security.master_key) == 32  # 256-bit key
        assert security.private_key is not None
        assert security.public_key is not None
        assert security.jwt_secret is not None
        assert len(security.jwt_secret) == 64  # 512-bit key
    
    def test_permissions_initialization(self, security):
        """Test permissions system initialization."""
        expected_roles = ["admin", "investigator", "analyst", "viewer"]
        
        for role in expected_roles:
            assert role in security.permissions
            assert isinstance(security.permissions[role], list)
            assert len(security.permissions[role]) > 0
        
        # Admin should have the most permissions
        admin_perms = len(security.permissions["admin"])
        viewer_perms = len(security.permissions["viewer"])
        assert admin_perms > viewer_perms
    
    @pytest.mark.asyncio
    async def test_successful_authentication(self, security):
        """Test successful user authentication."""
        username = "admin"
        password = "admin123"
        ip_address = "*************"
        
        context = await security.authenticate_user_async(username, password, ip_address)
        
        assert context is not None
        assert isinstance(context, SecurityContext)
        assert context.user_id == "admin_001"
        assert context.session_id is not None
        assert len(context.permissions) > 0
        assert context.ip_address == ip_address
        assert context.expires_at > datetime.now()
    
    @pytest.mark.asyncio
    async def test_failed_authentication_invalid_user(self, security):
        """Test failed authentication with invalid user."""
        username = "nonexistent"
        password = "password"
        
        context = await security.authenticate_user_async(username, password)
        
        assert context is None
        # Should have recorded failed attempt
        assert len(security.access_attempts) > 0
        last_attempt = security.access_attempts[-1]
        assert not last_attempt.success
        assert last_attempt.failure_reason == "user_not_found"
    
    @pytest.mark.asyncio
    async def test_failed_authentication_invalid_password(self, security):
        """Test failed authentication with invalid password."""
        username = "admin"
        password = "wrongpassword"
        
        context = await security.authenticate_user_async(username, password)
        
        assert context is None
        # Should have recorded failed attempt
        assert len(security.access_attempts) > 0
        last_attempt = security.access_attempts[-1]
        assert not last_attempt.success
        assert last_attempt.failure_reason == "invalid_password"
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, security):
        """Test rate limiting functionality."""
        username = "admin"
        password = "wrongpassword"
        ip_address = "*************"
        
        # Make multiple failed attempts
        for i in range(6):  # Exceed the limit of 5
            await security.authenticate_user_async(username, password, ip_address)
        
        # Next attempt should be rate limited
        context = await security.authenticate_user_async(username, password, ip_address)
        assert context is None
        
        # Should have generated security alert
        assert len(security.security_alerts) > 0
        alert = security.security_alerts[-1]
        assert alert.alert_type == "rate_limit_exceeded"
    
    @pytest.mark.asyncio
    async def test_authorization_check_valid(self, security):
        """Test authorization check with valid permissions."""
        # First authenticate
        context = await security.authenticate_user_async("admin", "admin123")
        assert context is not None
        
        # Test authorization
        authorized = await security.check_authorization_async(
            context.session_id, "investigations", "read", "confidential"
        )
        
        assert authorized is True
    
    @pytest.mark.asyncio
    async def test_authorization_check_invalid_session(self, security):
        """Test authorization check with invalid session."""
        authorized = await security.check_authorization_async(
            "invalid_session", "investigations", "read", "confidential"
        )
        
        assert authorized is False
    
    @pytest.mark.asyncio
    async def test_authorization_check_insufficient_permissions(self, security):
        """Test authorization check with insufficient permissions."""
        # Authenticate as viewer (limited permissions)
        # Note: This would require adding a viewer user to the mock data
        # For now, test with admin but restricted resource
        context = await security.authenticate_user_async("admin", "admin123")
        assert context is not None
        
        # Test with very high classification
        authorized = await security.check_authorization_async(
            context.session_id, "classified_data", "write", "top_secret"
        )
        
        # Even admin might not have access to write classified data
        # This depends on the specific implementation
        assert isinstance(authorized, bool)
    
    @pytest.mark.asyncio
    async def test_data_encryption(self, security):
        """Test data encryption functionality."""
        test_data = {
            "sensitive_info": "This is confidential data",
            "transaction_id": "tx123456",
            "amount": 1.5
        }
        
        encrypted_package = await security.encrypt_sensitive_data_async(
            test_data, "confidential"
        )
        
        assert encrypted_package is not None
        assert "encrypted_data" in encrypted_package
        assert "classification" in encrypted_package
        assert "integrity_proof" in encrypted_package
        assert encrypted_package["classification"] == "confidential"
        assert encrypted_package["encryption_algorithm"] == "AES-256"
    
    def test_input_sanitization_address(self, security):
        """Test address input sanitization."""
        # Valid address
        valid_address = "**********************************"
        sanitized = security.sanitize_input(valid_address, "address")
        assert sanitized == valid_address
        
        # Address with whitespace
        address_with_space = "  **********************************  "
        sanitized = security.sanitize_input(address_with_space, "address")
        assert sanitized == valid_address
        
        # Invalid address (too short)
        with pytest.raises(SecurityError):
            security.sanitize_input("short", "address")
    
    def test_input_sanitization_txid(self, security):
        """Test transaction ID input sanitization."""
        # Valid txid
        valid_txid = "a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890"
        sanitized = security.sanitize_input(valid_txid, "txid")
        assert sanitized == valid_txid.lower()
        
        # Invalid txid (wrong length)
        with pytest.raises(SecurityError):
            security.sanitize_input("short", "txid")
        
        # Invalid txid (non-hex characters)
        with pytest.raises(SecurityError):
            security.sanitize_input("g" * 64, "txid")
    
    def test_input_sanitization_amount(self, security):
        """Test amount input sanitization."""
        # Valid amounts
        assert security.sanitize_input("1.5", "amount") == 1.5
        assert security.sanitize_input(2.5, "amount") == 2.5
        assert security.sanitize_input("0", "amount") == 0.0
        
        # Invalid amounts
        with pytest.raises(SecurityError):
            security.sanitize_input("-1", "amount")  # Negative
        
        with pytest.raises(SecurityError):
            security.sanitize_input("25000000", "amount")  # Too large
        
        with pytest.raises(SecurityError):
            security.sanitize_input("invalid", "amount")  # Non-numeric
    
    def test_input_sanitization_string(self, security):
        """Test string input sanitization."""
        # Normal string
        normal_string = "This is a normal string"
        sanitized = security.sanitize_input(normal_string, "string")
        assert sanitized == normal_string
        
        # String with dangerous characters
        dangerous_string = "<script>alert('xss')</script>"
        sanitized = security.sanitize_input(dangerous_string, "string")
        assert "<" not in sanitized
        assert ">" not in sanitized
        assert "script" in sanitized  # Content should remain, just tags removed
    
    def test_security_status(self, security):
        """Test security status retrieval."""
        status = security.get_security_status()
        
        assert status is not None
        assert "active_sessions" in status
        assert "recent_alerts" in status
        assert "failed_attempts_last_hour" in status
        assert "security_level" in status
        assert "encryption_status" in status
        assert "monitoring_status" in status
        
        assert status["security_level"] == "high"
        assert status["encryption_status"] == "active"
        assert status["monitoring_status"] == "active"


class TestFaultToleranceManager:
    """Test cases for FaultToleranceManager."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return InvestigationConfig(
            investigation_id="test_fault_tolerance",
            target_address="**********************************",
            max_depth=3,
            api_key="test_key"
        )
    
    @pytest.fixture
    def fault_manager(self, config):
        """Create FaultToleranceManager instance."""
        return FaultToleranceManager(config)
    
    def test_fault_manager_initialization(self, fault_manager):
        """Test fault tolerance manager initialization."""
        assert fault_manager is not None
        assert fault_manager.config is not None
        assert fault_manager.recovery_actions is not None
        assert fault_manager.circuit_breakers is not None
        assert fault_manager.backup_locations is not None
        assert len(fault_manager.recovery_actions) > 0
        assert len(fault_manager.circuit_breakers) > 0
        assert len(fault_manager.backup_locations) > 0
    
    def test_recovery_actions_initialization(self, fault_manager):
        """Test recovery actions initialization."""
        expected_actions = [
            "memory_exhaustion", "api_failure", "database_failure", "disk_space_low"
        ]
        
        for action_id in expected_actions:
            assert action_id in fault_manager.recovery_actions
            action = fault_manager.recovery_actions[action_id]
            assert isinstance(action, RecoveryAction)
            assert action.action_id is not None
            assert action.priority > 0
            assert action.timeout_seconds > 0
            assert action.retry_count > 0
    
    def test_circuit_breakers_initialization(self, fault_manager):
        """Test circuit breakers initialization."""
        expected_breakers = ["blockchain_api", "database", "external_services"]
        
        for breaker_id in expected_breakers:
            assert breaker_id in fault_manager.circuit_breakers
            breaker = fault_manager.circuit_breakers[breaker_id]
            assert breaker["state"] == "closed"
            assert breaker["failure_count"] == 0
            assert breaker["failure_threshold"] > 0
            assert breaker["timeout"] > 0
    
    @pytest.mark.asyncio
    async def test_system_state_capture(self, fault_manager):
        """Test system state capture."""
        state = await fault_manager._capture_system_state()
        
        assert isinstance(state, SystemState)
        assert state.state_id is not None
        assert state.timestamp is not None
        assert state.component_states is not None
        assert state.memory_usage >= 0
        assert state.cpu_usage >= 0
        assert state.disk_usage >= 0
        assert state.network_status is not None
    
    @pytest.mark.asyncio
    async def test_failure_handling(self, fault_manager):
        """Test failure detection and handling."""
        await fault_manager._handle_failure(
            "test_failure", "test_component", "high", "Test failure message"
        )
        
        assert len(fault_manager.failure_events) > 0
        failure = fault_manager.failure_events[-1]
        assert failure.failure_type == "test_failure"
        assert failure.component == "test_component"
        assert failure.severity == "high"
        assert failure.error_message == "Test failure message"
    
    def test_circuit_breaker_failure_recording(self, fault_manager):
        """Test circuit breaker failure recording."""
        service = "blockchain_api"
        
        # Record multiple failures
        for i in range(3):
            should_call = fault_manager.record_service_failure(service)
            if i < 2:
                assert should_call is True
            # On the 5th failure (threshold), circuit should open
        
        # Record enough failures to open circuit
        for i in range(5):
            fault_manager.record_service_failure(service)
        
        # Circuit should now be open
        breaker = fault_manager.circuit_breakers[service]
        assert breaker["state"] == "open"
        
        # Further calls should be blocked
        should_call = fault_manager.record_service_failure(service)
        assert should_call is False
    
    def test_circuit_breaker_success_recording(self, fault_manager):
        """Test circuit breaker success recording."""
        service = "blockchain_api"
        
        # First record some failures
        fault_manager.record_service_failure(service)
        fault_manager.record_service_failure(service)
        
        # Then record success
        fault_manager.record_service_success(service)
        
        breaker = fault_manager.circuit_breakers[service]
        assert breaker["failure_count"] == 1  # Should decrease
    
    @pytest.mark.asyncio
    async def test_backup_creation(self, fault_manager):
        """Test backup creation functionality."""
        backup_info = await fault_manager.create_backup_async("full")
        
        assert backup_info is not None
        assert "backup_id" in backup_info
        assert "backup_type" in backup_info
        assert "timestamp" in backup_info
        assert "size_bytes" in backup_info
        assert "files" in backup_info
        assert "status" in backup_info
        
        assert backup_info["backup_type"] == "full"
        assert backup_info["status"] == "completed"
        assert backup_info["size_bytes"] > 0
    
    @pytest.mark.asyncio
    async def test_backup_data_collection(self, fault_manager):
        """Test backup data collection."""
        backup_data = await fault_manager._collect_backup_data("incremental")
        
        assert backup_data is not None
        assert "metadata" in backup_data
        assert "system_states" in backup_data
        assert "failure_events" in backup_data
        assert "configuration" in backup_data
        
        assert backup_data["metadata"]["backup_type"] == "incremental"
        assert backup_data["metadata"]["version"] == "3.0"
    
    @pytest.mark.asyncio
    async def test_recovery_execution(self, fault_manager):
        """Test recovery action execution."""
        # Create a test failure event
        failure_event = FailureEvent(
            failure_id="test_failure",
            component="test_component",
            failure_type="memory_exhaustion",
            severity="high",
            timestamp=datetime.now(),
            error_message="Test memory exhaustion"
        )
        
        await fault_manager._attempt_recovery(failure_event)
        
        assert failure_event.recovery_attempted is True
        # Recovery should succeed for memory_exhaustion
        assert failure_event.recovery_successful is True
    
    def test_system_status(self, fault_manager):
        """Test system status retrieval."""
        status = fault_manager.get_system_status()
        
        assert status is not None
        assert "monitoring_active" in status
        assert "system_health" in status
        assert "recent_failures" in status
        assert "circuit_breakers" in status
        assert "backup_locations" in status
        assert "recovery_actions_available" in status
        
        assert status["system_health"] in ["healthy", "degraded"]
        assert status["recovery_actions_available"] > 0


@pytest.mark.integration
class TestSecurityIntegration:
    """Integration tests for security components."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return InvestigationConfig(
            investigation_id="test_security_integration",
            target_address="**********************************",
            max_depth=3,
            api_key="test_key"
        )
    
    @pytest.mark.asyncio
    async def test_security_and_fault_tolerance_integration(self, config):
        """Test integration between security and fault tolerance systems."""
        security = EnhancedSecurity(config)
        fault_manager = FaultToleranceManager(config)
        
        # Test authentication
        context = await security.authenticate_user_async("admin", "admin123")
        assert context is not None
        
        # Test authorization
        authorized = await security.check_authorization_async(
            context.session_id, "system", "monitor", "internal"
        )
        assert authorized is True
        
        # Test system monitoring
        system_status = fault_manager.get_system_status()
        security_status = security.get_security_status()
        
        assert system_status is not None
        assert security_status is not None
        
        # Both systems should be operational
        assert system_status["system_health"] == "healthy"
        assert security_status["security_level"] == "high"
    
    @pytest.mark.asyncio
    async def test_security_under_attack_simulation(self, config):
        """Test security system under simulated attack."""
        security = EnhancedSecurity(config)
        
        # Simulate brute force attack
        attacker_ip = "*************"
        
        for i in range(10):
            await security.authenticate_user_async(
                f"user{i}", "wrongpassword", attacker_ip
            )
        
        # Should have triggered rate limiting and alerts
        assert len(security.security_alerts) > 0
        assert len(security.failed_attempts) > 0
        
        # Further attempts should be blocked
        context = await security.authenticate_user_async(
            "admin", "wrongpassword", attacker_ip
        )
        assert context is None
    
    @pytest.mark.asyncio
    async def test_fault_tolerance_under_stress(self, config):
        """Test fault tolerance system under stress."""
        fault_manager = FaultToleranceManager(config)
        
        # Simulate multiple service failures
        services = ["blockchain_api", "database", "external_services"]
        
        for service in services:
            for i in range(10):  # Exceed failure thresholds
                fault_manager.record_service_failure(service)
        
        # All circuits should be open
        for service in services:
            breaker = fault_manager.circuit_breakers[service]
            assert breaker["state"] == "open"
        
        # System should still be operational but degraded
        status = fault_manager.get_system_status()
        assert status["system_health"] in ["healthy", "degraded"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
