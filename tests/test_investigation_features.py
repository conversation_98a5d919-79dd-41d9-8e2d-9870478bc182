"""
Tests for professional investigation features.

Tests the victim recovery manager and law enforcement integration.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from cryptoforensics.models import TransactionInfo
from cryptoforensics.core.config import InvestigationConfig
from cryptoforensics.security.enhanced_security import EnhancedSecurity, SecurityContext
from cryptoforensics.investigation.victim_recovery import (
    VictimRecoveryManager, VictimCase, FundMovementAlert, RecoveryStatus, AlertPriority
)
from cryptoforensics.investigation.law_enforcement import (
    LawEnforcementIntegration, LECase, LEReport, IntelligenceAlert, 
    CaseClassification, ReportType, AgencyType
)
from cryptoforensics.exceptions import InvestigationError, SecurityError


class TestVictimRecoveryManager:
    """Test cases for VictimRecoveryManager."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return InvestigationConfig(
            investigation_id="test_victim_recovery",
            target_address="**********************************",
            max_depth=3,
            api_key="test_key"
        )
    
    @pytest.fixture
    def recovery_manager(self, config):
        """Create VictimRecoveryManager instance."""
        return VictimRecoveryManager(config)
    
    def test_recovery_manager_initialization(self, recovery_manager):
        """Test recovery manager initialization."""
        assert recovery_manager is not None
        assert recovery_manager.config is not None
        assert recovery_manager.active_cases is not None
        assert recovery_manager.exchange_contacts is not None
        assert recovery_manager.monitoring_addresses is not None
        assert len(recovery_manager.exchange_contacts) > 0
    
    def test_exchange_contacts_initialization(self, recovery_manager):
        """Test exchange contacts initialization."""
        expected_exchanges = ["binance", "coinbase", "kraken", "bitfinex"]
        
        for exchange_id in expected_exchanges:
            assert exchange_id in recovery_manager.exchange_contacts
            contact = recovery_manager.exchange_contacts[exchange_id]
            assert contact.exchange_id == exchange_id
            assert contact.exchange_name is not None
            assert contact.contact_email is not None
            assert contact.jurisdiction is not None
            assert contact.cooperation_level in ["high", "medium", "low"]
    
    @pytest.mark.asyncio
    async def test_create_recovery_case(self, recovery_manager):
        """Test recovery case creation."""
        victim_info = {
            "victim_id": "victim_001",
            "victim_addresses": ["1VictimAddress123"]
        }
        
        incident_details = {
            "incident_date": "2023-01-01T10:00:00Z",
            "stolen_amount": 50.5,
            "currency": "BTC",
            "theft_addresses": ["1TheftAddress123", "1TheftAddress456"],
            "investigator": "detective_smith",
            "description": "Cryptocurrency theft via phishing attack"
        }
        
        case = await recovery_manager.create_recovery_case_async(victim_info, incident_details)
        
        assert isinstance(case, VictimCase)
        assert case.case_id is not None
        assert case.victim_id == "victim_001"
        assert case.stolen_amount == 50.5
        assert case.currency == "BTC"
        assert case.status == RecoveryStatus.INITIATED
        assert case.assigned_investigator == "detective_smith"
        assert len(case.theft_addresses) == 2
        
        # Case should be stored
        assert case.case_id in recovery_manager.active_cases
        
        # Theft addresses should be monitored
        for addr in case.theft_addresses:
            assert addr in recovery_manager.monitoring_addresses
    
    def test_case_priority_calculation(self, recovery_manager):
        """Test case priority calculation."""
        # High amount, recent incident
        recent_date = datetime.now() - timedelta(days=1)
        priority = recovery_manager._calculate_case_priority(150.0, recent_date)
        assert priority == AlertPriority.CRITICAL
        
        # Medium amount, recent incident
        priority = recovery_manager._calculate_case_priority(15.0, recent_date)
        assert priority == AlertPriority.HIGH
        
        # Low amount, old incident
        old_date = datetime.now() - timedelta(days=60)
        priority = recovery_manager._calculate_case_priority(0.5, old_date)
        assert priority == AlertPriority.LOW
    
    @pytest.mark.asyncio
    async def test_fund_movement_monitoring(self, recovery_manager):
        """Test fund movement monitoring."""
        # First create a case
        victim_info = {"victim_id": "victim_001"}
        incident_details = {
            "stolen_amount": 10.0,
            "theft_addresses": ["1TheftAddr123"]
        }
        
        case = await recovery_manager.create_recovery_case_async(victim_info, incident_details)
        
        # Create transactions involving monitored addresses
        transactions = [
            TransactionInfo(
                txid="tx1",
                from_address="1TheftAddr123",
                to_address="1ExchangeAddr456",
                amount_btc=5.0,
                timestamp="2023-01-01T12:00:00Z",
                depth=1,
                confirmations=6
            ),
            TransactionInfo(
                txid="tx2",
                from_address="1OtherAddr789",
                to_address="1NormalAddr000",
                amount_btc=1.0,
                timestamp="2023-01-01T13:00:00Z",
                depth=1,
                confirmations=6
            )
        ]
        
        alerts = await recovery_manager.monitor_fund_movement_async(transactions)
        
        assert len(alerts) == 1  # Only one transaction involves monitored address
        alert = alerts[0]
        assert isinstance(alert, FundMovementAlert)
        assert alert.case_id == case.case_id
        assert alert.amount_moved == 5.0
        assert "1TheftAddr123" in alert.affected_addresses
    
    @pytest.mark.asyncio
    async def test_exchange_coordination(self, recovery_manager):
        """Test exchange coordination functionality."""
        # Create a case first
        victim_info = {"victim_id": "victim_001"}
        incident_details = {
            "stolen_amount": 25.0,
            "investigator": "detective_jones"
        }
        
        case = await recovery_manager.create_recovery_case_async(victim_info, incident_details)
        
        # Test coordination with exchange
        request_package = await recovery_manager.coordinate_with_exchange_async(
            case.case_id, "binance", "freeze"
        )
        
        assert request_package is not None
        assert request_package["case_id"] == case.case_id
        assert request_package["request_type"] == "freeze"
        assert request_package["exchange"] == "binance"
        assert "victim_info" in request_package
        assert "contact_info" in request_package
        
        # Case status should be updated
        updated_case = recovery_manager.active_cases[case.case_id]
        assert updated_case.status == RecoveryStatus.FREEZE_REQUESTED
        assert updated_case.recovery_progress["freeze_requests_sent"] == 1
    
    @pytest.mark.asyncio
    async def test_case_status_update(self, recovery_manager):
        """Test case status update."""
        # Create a case
        victim_info = {"victim_id": "victim_001"}
        incident_details = {"stolen_amount": 10.0}
        
        case = await recovery_manager.create_recovery_case_async(victim_info, incident_details)
        original_status = case.status
        
        # Update status
        new_status = RecoveryStatus.FUNDS_LOCATED
        await recovery_manager.update_case_status_async(
            case.case_id, new_status, "Funds located on exchange"
        )
        
        # Verify update
        updated_case = recovery_manager.active_cases[case.case_id]
        assert updated_case.status == new_status
        assert updated_case.status != original_status
        assert updated_case.updated_at > case.created_at
    
    def test_case_summary(self, recovery_manager):
        """Test case summary generation."""
        # This test requires a case to exist, so we'll create one synchronously
        # In a real scenario, you'd use the async method
        case_id = "test_case_001"
        case = VictimCase(
            case_id=case_id,
            victim_id="victim_001",
            incident_date=datetime.now(),
            stolen_amount=15.0,
            currency="BTC",
            theft_addresses=["addr1", "addr2"],
            victim_addresses=["victim_addr"],
            status=RecoveryStatus.INVESTIGATING,
            assigned_investigator="detective_smith",
            priority=AlertPriority.HIGH,
            description="Test case",
            evidence_items=[],
            recovery_progress={"funds_traced": 5.0},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        recovery_manager.active_cases[case_id] = case
        
        summary = recovery_manager.get_case_summary(case_id)
        
        assert summary is not None
        assert "case_info" in summary
        assert "progress" in summary
        assert "alerts" in summary
        assert "actions_taken" in summary
        
        assert summary["case_info"]["case_id"] == case_id
        assert summary["case_info"]["stolen_amount"] == 15.0
        assert summary["case_info"]["status"] == RecoveryStatus.INVESTIGATING.value
    
    def test_active_alerts_retrieval(self, recovery_manager):
        """Test active alerts retrieval."""
        # Add some test alerts
        alert1 = FundMovementAlert(
            alert_id="alert_001",
            case_id="case_001",
            alert_type="fund_movement",
            priority=AlertPriority.HIGH,
            description="Large fund movement detected",
            affected_addresses=["addr1"],
            transaction_ids=["tx1"],
            amount_moved=10.0,
            destination_info={},
            timestamp=datetime.now()
        )
        
        alert2 = FundMovementAlert(
            alert_id="alert_002",
            case_id="case_002",
            alert_type="exchange_deposit",
            priority=AlertPriority.CRITICAL,
            description="Funds deposited to exchange",
            affected_addresses=["addr2"],
            transaction_ids=["tx2"],
            amount_moved=25.0,
            destination_info={},
            timestamp=datetime.now(),
            acknowledged=True  # This one is acknowledged
        )
        
        recovery_manager.active_alerts["alert_001"] = alert1
        recovery_manager.active_alerts["alert_002"] = alert2
        
        # Get all active alerts
        alerts = recovery_manager.get_active_alerts()
        assert len(alerts) == 1  # Only unacknowledged alerts
        assert alerts[0]["alert_id"] == "alert_001"
        
        # Get alerts by priority
        high_alerts = recovery_manager.get_active_alerts(AlertPriority.HIGH)
        assert len(high_alerts) == 1
        assert high_alerts[0]["priority"] == "high"
        
        critical_alerts = recovery_manager.get_active_alerts(AlertPriority.CRITICAL)
        assert len(critical_alerts) == 0  # Critical alert is acknowledged
    
    def test_alert_acknowledgment(self, recovery_manager):
        """Test alert acknowledgment."""
        alert = FundMovementAlert(
            alert_id="alert_test",
            case_id="case_test",
            alert_type="test",
            priority=AlertPriority.MEDIUM,
            description="Test alert",
            affected_addresses=[],
            transaction_ids=[],
            amount_moved=0.0,
            destination_info={},
            timestamp=datetime.now()
        )
        
        recovery_manager.active_alerts["alert_test"] = alert
        
        # Acknowledge alert
        result = recovery_manager.acknowledge_alert("alert_test")
        assert result is True
        assert alert.acknowledged is True
        
        # Try to acknowledge non-existent alert
        result = recovery_manager.acknowledge_alert("non_existent")
        assert result is False


class TestLawEnforcementIntegration:
    """Test cases for LawEnforcementIntegration."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return InvestigationConfig(
            investigation_id="test_le_integration",
            target_address="**********************************",
            max_depth=3,
            api_key="test_key"
        )
    
    @pytest.fixture
    def security(self, config):
        """Create EnhancedSecurity instance."""
        return EnhancedSecurity(config)
    
    @pytest.fixture
    def le_integration(self, config, security):
        """Create LawEnforcementIntegration instance."""
        return LawEnforcementIntegration(config, security)
    
    @pytest.fixture
    async def security_context(self, security):
        """Create authenticated security context."""
        context = await security.authenticate_user_async("admin", "admin123")
        return context
    
    def test_le_integration_initialization(self, le_integration):
        """Test law enforcement integration initialization."""
        assert le_integration is not None
        assert le_integration.config is not None
        assert le_integration.security is not None
        assert le_integration.agencies is not None
        assert len(le_integration.agencies) > 0
    
    def test_agencies_initialization(self, le_integration):
        """Test agencies database initialization."""
        expected_agencies = ["fbi_cyber", "secret_service", "finra", "interpol"]
        
        for agency_id in expected_agencies:
            assert agency_id in le_integration.agencies
            agency = le_integration.agencies[agency_id]
            assert agency.agency_id == agency_id
            assert agency.agency_name is not None
            assert isinstance(agency.agency_type, AgencyType)
            assert agency.jurisdiction is not None
            assert agency.clearance_level is not None
    
    @pytest.mark.asyncio
    async def test_create_le_case(self, le_integration, security_context):
        """Test law enforcement case creation."""
        case_info = {
            "le_case_number": "FBI-2023-001",
            "agency_id": "fbi_cyber",
            "case_type": "cryptocurrency_fraud",
            "subject_addresses": ["1SuspectAddr123", "1SuspectAddr456"],
            "assigned_agents": ["agent_smith", "agent_jones"],
            "priority": "high"
        }
        
        case = await le_integration.create_le_case_async(case_info, security_context)
        
        assert isinstance(case, LECase)
        assert case.case_id is not None
        assert case.le_case_number == "FBI-2023-001"
        assert case.agency_id == "fbi_cyber"
        assert case.case_type == "cryptocurrency_fraud"
        assert len(case.subject_addresses) == 2
        assert case.status == "active"
        assert case.priority == "high"
        
        # Case should be stored
        assert case.case_id in le_integration.active_cases
    
    def test_classification_determination(self, le_integration):
        """Test case classification determination."""
        # Test terrorism case
        terrorism_case = {
            "case_type": "terrorism_financing",
            "amount": 50000
        }
        classification = le_integration._determine_classification(terrorism_case)
        assert classification == CaseClassification.SECRET
        
        # Test high-value case
        high_value_case = {
            "case_type": "money_laundering",
            "amount": 2000000
        }
        classification = le_integration._determine_classification(high_value_case)
        assert classification == CaseClassification.CONFIDENTIAL
        
        # Test normal case
        normal_case = {
            "case_type": "fraud",
            "amount": 5000
        }
        classification = le_integration._determine_classification(normal_case)
        assert classification == CaseClassification.UNCLASSIFIED
    
    @pytest.mark.asyncio
    async def test_generate_le_report(self, le_integration, security_context):
        """Test law enforcement report generation."""
        # First create a case
        case_info = {
            "le_case_number": "TEST-001",
            "agency_id": "fbi_cyber",
            "case_type": "investigation"
        }
        
        case = await le_integration.create_le_case_async(case_info, security_context)
        
        # Generate SAR report
        report = await le_integration.generate_le_report_async(
            case.case_id, ReportType.SAR, security_context
        )
        
        assert isinstance(report, LEReport)
        assert report.report_id is not None
        assert report.report_type == ReportType.SAR
        assert report.case_id == case.case_id
        assert report.agency_id == case.agency_id
        assert report.classification == case.classification
        assert report.status == "draft"
        assert "suspicious_activities" in report.content
        assert "subject_information" in report.content
    
    @pytest.mark.asyncio
    async def test_intelligence_sharing(self, le_integration, security_context):
        """Test intelligence sharing functionality."""
        alert_info = {
            "alert_type": "threat_indicator",
            "priority": "high",
            "source_agency": "fbi_cyber",
            "subject_addresses": ["1ThreatAddr123"],
            "threat_indicators": ["large_transactions", "mixing_behavior"],
            "classification": "confidential",
            "content": {
                "threat_description": "Suspected money laundering operation",
                "confidence_level": "high"
            }
        }
        
        target_agencies = ["secret_service", "finra"]
        
        alert = await le_integration.share_intelligence_async(
            alert_info, target_agencies, security_context
        )
        
        assert isinstance(alert, IntelligenceAlert)
        assert alert.alert_id is not None
        assert alert.alert_type == "threat_indicator"
        assert alert.priority == "high"
        assert alert.source_agency == "fbi_cyber"
        assert alert.target_agencies == target_agencies
        assert len(alert.subject_addresses) == 1
        assert alert.classification == CaseClassification.CONFIDENTIAL
    
    def test_agency_info_retrieval(self, le_integration):
        """Test agency information retrieval."""
        # Test existing agency
        agency_info = le_integration.get_agency_info("fbi_cyber")
        assert agency_info is not None
        assert agency_info["agency_id"] == "fbi_cyber"
        assert agency_info["agency_name"] == "FBI Cyber Division"
        assert agency_info["agency_type"] == "fbi"
        assert agency_info["data_sharing_agreement"] is True
        
        # Test non-existent agency
        agency_info = le_integration.get_agency_info("non_existent")
        assert agency_info is None
    
    def test_case_status_retrieval(self, le_integration):
        """Test case status retrieval."""
        # Create a test case
        case_id = "test_case_001"
        case = LECase(
            case_id=case_id,
            le_case_number="TEST-001",
            agency_id="fbi_cyber",
            classification=CaseClassification.CONFIDENTIAL,
            case_type="investigation",
            subject_addresses=["addr1"],
            investigation_start=datetime.now(),
            assigned_agents=["agent1"],
            status="active",
            priority="high",
            evidence_items=["evidence1", "evidence2"],
            related_cases=[],
            sharing_restrictions=[]
        )
        
        le_integration.active_cases[case_id] = case
        
        status = le_integration.get_case_status(case_id)
        assert status is not None
        assert status["case_id"] == case_id
        assert status["le_case_number"] == "TEST-001"
        assert status["agency_id"] == "fbi_cyber"
        assert status["status"] == "active"
        assert status["priority"] == "high"
        assert status["classification"] == "confidential"
        assert status["evidence_items"] == 2
    
    def test_pending_reports_retrieval(self, le_integration):
        """Test pending reports retrieval."""
        # Create test reports
        report1 = LEReport(
            report_id="report_001",
            report_type=ReportType.SAR,
            case_id="case_001",
            agency_id="fbi_cyber",
            classification=CaseClassification.CONFIDENTIAL,
            content={},
            generated_by="agent1",
            generated_at=datetime.now(),
            submitted_at=None,
            status="draft"
        )
        
        report2 = LEReport(
            report_id="report_002",
            report_type=ReportType.INCIDENT,
            case_id="case_002",
            agency_id="secret_service",
            classification=CaseClassification.UNCLASSIFIED,
            content={},
            generated_by="agent2",
            generated_at=datetime.now(),
            submitted_at=None,
            status="draft"
        )
        
        le_integration.pending_reports["report_001"] = report1
        le_integration.pending_reports["report_002"] = report2
        
        # Get all pending reports
        all_reports = le_integration.get_pending_reports()
        assert len(all_reports) == 2
        
        # Get reports for specific agency
        fbi_reports = le_integration.get_pending_reports("fbi_cyber")
        assert len(fbi_reports) == 1
        assert fbi_reports[0]["agency_id"] == "fbi_cyber"
    
    def test_data_sharing_audit(self, le_integration):
        """Test data sharing audit log."""
        # The audit log should be populated by various operations
        # For this test, we'll check the structure
        audit_log = le_integration.get_data_sharing_audit(30)
        
        assert isinstance(audit_log, list)
        # Initially empty, but structure should be correct
        
        # Add a test log entry manually
        test_event = {
            "event_id": "test_event",
            "event_type": "test",
            "resource_id": "test_resource",
            "user_id": "test_user",
            "agency_id": "test_agency",
            "classification": "unclassified",
            "timestamp": datetime.now().isoformat(),
            "ip_address": "127.0.0.1",
            "user_agent": "test"
        }
        
        le_integration.data_sharing_log.append(test_event)
        
        audit_log = le_integration.get_data_sharing_audit(30)
        assert len(audit_log) == 1
        assert audit_log[0]["event_id"] == "test_event"


@pytest.mark.integration
class TestInvestigationIntegration:
    """Integration tests for investigation components."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return InvestigationConfig(
            investigation_id="test_investigation_integration",
            target_address="**********************************",
            max_depth=3,
            api_key="test_key"
        )
    
    @pytest.mark.asyncio
    async def test_victim_recovery_to_le_integration_workflow(self, config):
        """Test workflow from victim recovery to law enforcement integration."""
        # Initialize components
        security = EnhancedSecurity(config)
        recovery_manager = VictimRecoveryManager(config)
        le_integration = LawEnforcementIntegration(config, security)
        
        # Authenticate user
        context = await security.authenticate_user_async("admin", "admin123")
        assert context is not None
        
        # Create victim recovery case
        victim_info = {
            "victim_id": "victim_integration_test",
            "victim_addresses": ["1VictimAddr123"]
        }
        
        incident_details = {
            "stolen_amount": 100.0,
            "currency": "BTC",
            "theft_addresses": ["1TheftAddr123"],
            "investigator": "detective_integration",
            "description": "Integration test case"
        }
        
        victim_case = await recovery_manager.create_recovery_case_async(
            victim_info, incident_details
        )
        
        assert victim_case is not None
        assert victim_case.status == RecoveryStatus.INITIATED
        
        # Escalate to law enforcement
        le_case_info = {
            "le_case_number": f"LE-{victim_case.case_id}",
            "agency_id": "fbi_cyber",
            "case_type": "cryptocurrency_theft",
            "subject_addresses": victim_case.theft_addresses,
            "assigned_agents": ["agent_integration"],
            "priority": "high"
        }
        
        le_case = await le_integration.create_le_case_async(le_case_info, context)
        
        assert le_case is not None
        assert le_case.agency_id == "fbi_cyber"
        assert le_case.subject_addresses == victim_case.theft_addresses
        
        # Generate report
        report = await le_integration.generate_le_report_async(
            le_case.case_id, ReportType.INCIDENT, context
        )
        
        assert report is not None
        assert report.case_id == le_case.case_id
        
        # Update victim case status
        await recovery_manager.update_case_status_async(
            victim_case.case_id, 
            RecoveryStatus.EXCHANGE_CONTACTED,
            f"Escalated to law enforcement case {le_case.le_case_number}"
        )
        
        # Verify final state
        updated_victim_case = recovery_manager.active_cases[victim_case.case_id]
        assert updated_victim_case.status == RecoveryStatus.EXCHANGE_CONTACTED
        
        le_case_status = le_integration.get_case_status(le_case.case_id)
        assert le_case_status["status"] == "active"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
