# CryptoForensics v3.0 API Reference

## Table of Contents

1. [Core Classes](#core-classes)
2. [Visualization API](#visualization-api)
3. [Security API](#security-api)
4. [Investigation API](#investigation-api)
5. [Analysis API](#analysis-api)
6. [Utilities API](#utilities-api)

## Core Classes

### InvestigationConfig

Configuration class for investigations.

```python
class InvestigationConfig:
    def __init__(
        self,
        investigation_id: str,
        target_address: str,
        max_depth: int = 3,
        api_key: Optional[str] = None,
        enable_3d_visualization: bool = True,
        enable_real_time_monitoring: bool = True,
        security_level: str = "high"
    )
```

**Parameters:**
- `investigation_id`: Unique identifier for the investigation
- `target_address`: Primary address to investigate
- `max_depth`: Maximum depth for transaction tracing
- `api_key`: Blockchain API key
- `enable_3d_visualization`: Enable 3D visualization features
- `enable_real_time_monitoring`: Enable real-time monitoring
- `security_level`: Security level ("low", "medium", "high")

### CryptoForensicsInvestigator

Main investigation class.

```python
class CryptoForensicsInvestigator:
    def __init__(self, config: InvestigationConfig)
    
    async def investigate_async(self) -> InvestigationResult
    def generate_report(self) -> ReportResult
    async def start_real_time_monitoring(self) -> None
```

## Visualization API

### Advanced3DVisualizer

3D transaction flow visualization.

```python
class Advanced3DVisualizer:
    def __init__(self, config: InvestigationConfig)
    
    async def create_3d_transaction_flow_async(
        self,
        transactions: List[TransactionInfo],
        config: Visualization3DConfig
    ) -> Dict[str, Any]
```

**Returns:**
```python
{
    "figure": plotly.graph_objects.Figure,
    "nodes": List[Node3D],
    "edges": List[Edge3D],
    "metadata": Dict[str, Any],
    "config": Dict[str, Any]
}
```

### Visualization3DConfig

Configuration for 3D visualizations.

```python
@dataclass
class Visualization3DConfig:
    layout_algorithm: str = "force_directed_3d"  # "temporal_3d", "hierarchical_3d"
    node_size_factor: float = 1.0
    edge_width_factor: float = 1.0
    color_scheme: str = "risk_based"  # "transaction_type", "temporal"
    animation_enabled: bool = True
    interactive_mode: bool = True
    clustering_enabled: bool = True
    time_dimension: bool = True
```

### RealTimeDashboard

Real-time monitoring dashboard.

```python
class RealTimeDashboard:
    def __init__(self, config: InvestigationConfig)
    
    async def start_monitoring_async(self) -> None
    async def stop_monitoring(self) -> None
    async def update_metrics_async(self, investigation_data: Dict[str, Any]) -> None
    async def generate_dashboard_async(self) -> go.Figure
    def get_metrics_summary(self) -> Dict[str, Any]
    def get_active_alerts(self) -> List[Dict[str, Any]]
```

### AdvancedAnalytics

Machine learning analytics engine.

```python
class AdvancedAnalytics:
    def __init__(self, config: InvestigationConfig)
    
    async def detect_behavioral_patterns_async(
        self, 
        transactions: List[TransactionInfo]
    ) -> List[BehavioralPattern]
    
    async def detect_anomalies_async(
        self, 
        transactions: List[TransactionInfo]
    ) -> List[AnomalyResult]
```

## Security API

### EnhancedSecurity

Enhanced security system.

```python
class EnhancedSecurity:
    def __init__(self, config: InvestigationConfig)
    
    async def authenticate_user_async(
        self,
        username: str,
        password: str,
        ip_address: Optional[str] = None
    ) -> Optional[SecurityContext]
    
    async def check_authorization_async(
        self,
        session_id: str,
        resource: str,
        action: str,
        data_classification: str = "internal"
    ) -> bool
    
    async def encrypt_sensitive_data_async(
        self,
        data: Dict[str, Any],
        classification: str = "confidential"
    ) -> Dict[str, Any]
    
    def sanitize_input(self, input_data: Any, input_type: str = "general") -> Any
    def get_security_status(self) -> Dict[str, Any]
```

### SecurityContext

User security context.

```python
@dataclass
class SecurityContext:
    user_id: str
    session_id: str
    permissions: List[str]
    access_level: str
    created_at: datetime
    expires_at: datetime
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
```

### FaultToleranceManager

Fault tolerance and disaster recovery.

```python
class FaultToleranceManager:
    def __init__(self, config: InvestigationConfig)
    
    async def start_monitoring_async(self) -> None
    async def stop_monitoring(self) -> None
    async def create_backup_async(self, backup_type: str = "full") -> Dict[str, Any]
    def record_service_failure(self, service: str) -> bool
    def record_service_success(self, service: str) -> None
    def get_system_status(self) -> Dict[str, Any]
```

## Investigation API

### VictimRecoveryManager

Victim fund recovery management.

```python
class VictimRecoveryManager:
    def __init__(self, config: InvestigationConfig)
    
    async def create_recovery_case_async(
        self,
        victim_info: Dict[str, Any],
        incident_details: Dict[str, Any]
    ) -> VictimCase
    
    async def monitor_fund_movement_async(
        self,
        transactions: List[TransactionInfo]
    ) -> List[FundMovementAlert]
    
    async def coordinate_with_exchange_async(
        self,
        case_id: str,
        exchange_name: str,
        request_type: str
    ) -> Dict[str, Any]
    
    async def update_case_status_async(
        self,
        case_id: str,
        new_status: RecoveryStatus,
        notes: Optional[str] = None
    ) -> None
```

### VictimCase

Victim recovery case data.

```python
@dataclass
class VictimCase:
    case_id: str
    victim_id: str
    incident_date: datetime
    stolen_amount: float
    currency: str
    theft_addresses: List[str]
    victim_addresses: List[str]
    status: RecoveryStatus
    assigned_investigator: str
    priority: AlertPriority
    description: str
    evidence_items: List[str]
    recovery_progress: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
```

### LawEnforcementIntegration

Law enforcement integration system.

```python
class LawEnforcementIntegration:
    def __init__(self, config: InvestigationConfig, security: EnhancedSecurity)
    
    async def create_le_case_async(
        self,
        case_info: Dict[str, Any],
        security_context: SecurityContext
    ) -> LECase
    
    async def generate_le_report_async(
        self,
        case_id: str,
        report_type: ReportType,
        security_context: SecurityContext
    ) -> LEReport
    
    async def share_intelligence_async(
        self,
        alert_info: Dict[str, Any],
        target_agencies: List[str],
        security_context: SecurityContext
    ) -> IntelligenceAlert
```

### LECase

Law enforcement case data.

```python
@dataclass
class LECase:
    case_id: str
    le_case_number: str
    agency_id: str
    classification: CaseClassification
    case_type: str
    subject_addresses: List[str]
    investigation_start: datetime
    assigned_agents: List[str]
    status: str
    priority: str
    evidence_items: List[str]
    related_cases: List[str]
    sharing_restrictions: List[str]
```

## Analysis API

### MixerDetector

Enhanced mixer detection.

```python
class MixerDetector:
    def __init__(self, config: InvestigationConfig)
    
    async def detect_mixing_async(
        self,
        transactions: List[TransactionInfo]
    ) -> List[MixingResult]
    
    async def analyze_mixing_patterns_async(
        self,
        address: str
    ) -> MixingAnalysis
```

### CrossChainTracker

Cross-chain transaction tracking.

```python
class CrossChainTracker:
    def __init__(self, config: InvestigationConfig)
    
    async def track_cross_chain_async(
        self,
        source_address: str,
        target_chains: List[str]
    ) -> List[CrossChainResult]
    
    async def analyze_bridge_activity_async(
        self,
        transactions: List[TransactionInfo]
    ) -> List[BridgeActivity]
```

### ChainAnalyzer

Advanced chain analysis.

```python
class ChainAnalyzer:
    def __init__(self, config: InvestigationConfig)
    
    async def analyze_transaction_chains_async(
        self,
        transactions: List[TransactionInfo]
    ) -> ChainAnalysisResult
    
    async def detect_peel_chains_async(
        self,
        address: str
    ) -> List[PeelChainResult]
```

## Utilities API

### CryptoUtils

Cryptographic utilities.

```python
class CryptoUtils:
    @staticmethod
    def generate_encryption_key() -> Tuple[bytes, bytes]
    
    @staticmethod
    def encrypt_data(data: Dict[str, Any], key: bytes) -> bytes
    
    @staticmethod
    def decrypt_data(encrypted_data: bytes, key: bytes) -> Dict[str, Any]
    
    @staticmethod
    def create_integrity_proof(data: Dict[str, Any], private_key) -> str
    
    @staticmethod
    def verify_integrity_proof(data: Dict[str, Any], proof: str, public_key) -> bool
```

### TimeUtils

Time and timestamp utilities.

```python
class TimeUtils:
    @staticmethod
    def parse_timestamp(timestamp_str: str) -> Optional[datetime]
    
    @staticmethod
    def format_timestamp(dt: datetime) -> str
    
    @staticmethod
    def get_time_range(start: datetime, end: datetime) -> timedelta
    
    @staticmethod
    def is_within_range(timestamp: datetime, start: datetime, end: datetime) -> bool
```

### PerformanceMonitor

Performance monitoring utilities.

```python
class PerformanceMonitor:
    def __init__(self)
    
    def start_monitoring(self, interval: float = 10.0) -> None
    def stop_monitoring(self) -> None
    def get_current_memory_usage(self) -> Dict[str, Any]
    def get_performance_summary(self) -> Dict[str, Any]
    
    @staticmethod
    def performance_monitor(operation_name: str):
        """Decorator for monitoring function performance."""
        pass
```

## Enums and Constants

### RecoveryStatus

```python
class RecoveryStatus(Enum):
    INITIATED = "initiated"
    INVESTIGATING = "investigating"
    FUNDS_LOCATED = "funds_located"
    EXCHANGE_CONTACTED = "exchange_contacted"
    FREEZE_REQUESTED = "freeze_requested"
    FUNDS_FROZEN = "funds_frozen"
    RECOVERY_IN_PROGRESS = "recovery_in_progress"
    FUNDS_RECOVERED = "funds_recovered"
    CASE_CLOSED = "case_closed"
```

### CaseClassification

```python
class CaseClassification(Enum):
    UNCLASSIFIED = "unclassified"
    CONFIDENTIAL = "confidential"
    SECRET = "secret"
    TOP_SECRET = "top_secret"
```

### ReportType

```python
class ReportType(Enum):
    SAR = "suspicious_activity_report"
    CTR = "currency_transaction_report"
    FINRA = "finra_report"
    INCIDENT = "incident_report"
    INTELLIGENCE = "intelligence_report"
    CASE_SUMMARY = "case_summary"
```

### AlertPriority

```python
class AlertPriority(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
```

## Error Handling

### Exception Classes

```python
class CryptoForensicsError(Exception):
    """Base exception for CryptoForensics."""
    pass

class SecurityError(CryptoForensicsError):
    """Security-related errors."""
    pass

class InvestigationError(CryptoForensicsError):
    """Investigation-related errors."""
    pass

class VisualizationError(CryptoForensicsError):
    """Visualization-related errors."""
    pass

class AnalysisError(CryptoForensicsError):
    """Analysis-related errors."""
    pass

class SystemError(CryptoForensicsError):
    """System-related errors."""
    pass
```

## Usage Examples

### Complete Investigation Workflow

```python
import asyncio
from cryptoforensics import *

async def main():
    # Configure investigation
    config = InvestigationConfig(
        investigation_id="case_001",
        target_address="**********************************",
        max_depth=3,
        api_key="your_api_key"
    )
    
    # Initialize security
    security = EnhancedSecurity(config)
    context = await security.authenticate_user_async("investigator", "password")
    
    # Run investigation
    investigator = CryptoForensicsInvestigator(config)
    result = await investigator.investigate_async()
    
    # Create 3D visualization
    visualizer = Advanced3DVisualizer(config)
    viz_config = Visualization3DConfig()
    viz_result = await visualizer.create_3d_transaction_flow_async(
        result.transactions, viz_config
    )
    
    # Start real-time monitoring
    dashboard = RealTimeDashboard(config)
    await dashboard.start_monitoring_async()
    
    # Generate report
    report = investigator.generate_report()
    print(f"Investigation completed: {report.file_path}")

if __name__ == "__main__":
    asyncio.run(main())
```

For more examples and detailed usage, see the [User Manual](user_manual_v3.md).
