# CryptoForensics v3.0 User Manual

## Table of Contents

1. [Introduction](#introduction)
2. [Getting Started](#getting-started)
3. [Enhanced Visualization](#enhanced-visualization)
4. [Security Features](#security-features)
5. [Professional Investigation Tools](#professional-investigation-tools)
6. [Advanced Anti-Evasion](#advanced-anti-evasion)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

## Introduction

CryptoForensics v3.0 represents a major advancement in cryptocurrency investigation capabilities. This manual covers the new features and enhanced functionality introduced in version 3.0.

### What's New in v3.0

- **3D Interactive Visualizations**: Immersive transaction flow analysis
- **Real-time Monitoring**: Live dashboard with alerts and metrics
- **Enhanced Security**: End-to-end encryption and access control
- **Professional Investigation Tools**: Victim recovery and law enforcement integration
- **Advanced Analytics**: Machine learning-based pattern recognition

## Getting Started

### Installation

```bash
# Install CryptoForensics v3.0
pip install cryptoforensics==3.0.0

# Install optional dependencies for full functionality
pip install -r requirements-full.txt
```

### Basic Configuration

```python
from cryptoforensics import InvestigationConfig

config = InvestigationConfig(
    investigation_id="case_001",
    target_address="**********************************",
    max_depth=3,
    api_key="your_blockchain_api_key",
    enable_3d_visualization=True,
    enable_real_time_monitoring=True,
    security_level="high"
)
```

### Authentication and Security

```python
from cryptoforensics.security import EnhancedSecurity

# Initialize security system
security = EnhancedSecurity(config)

# Authenticate user
context = await security.authenticate_user_async(
    username="investigator",
    password="secure_password",
    ip_address="*************"
)

if context:
    print(f"Authenticated as {context.user_id}")
    print(f"Access level: {context.access_level}")
    print(f"Permissions: {context.permissions}")
```

## Enhanced Visualization

### 3D Transaction Flow Visualization

Create immersive 3D visualizations of transaction flows:

```python
from cryptoforensics.visualization import Advanced3DVisualizer, Visualization3DConfig

# Initialize 3D visualizer
visualizer = Advanced3DVisualizer(config)

# Configure 3D visualization
viz_config = Visualization3DConfig(
    layout_algorithm="force_directed_3d",  # or "temporal_3d", "hierarchical_3d"
    color_scheme="risk_based",             # or "transaction_type", "temporal"
    animation_enabled=True,
    interactive_mode=True,
    clustering_enabled=True,
    time_dimension=True
)

# Generate 3D visualization
viz_result = await visualizer.create_3d_transaction_flow_async(
    transactions=investigation_result.transactions,
    config=viz_config
)

# Access the interactive figure
fig = viz_result["figure"]
fig.show()  # Opens in browser

# Save visualization
fig.write_html("transaction_flow_3d.html")
```

### Real-time Monitoring Dashboard

Set up live monitoring with customizable alerts:

```python
from cryptoforensics.visualization import RealTimeDashboard, DashboardConfig

# Configure dashboard
dashboard_config = DashboardConfig(
    refresh_interval=30,  # seconds
    max_data_points=1000,
    enable_alerts=True,
    enable_auto_refresh=True,
    theme="dark",  # or "light"
    layout="grid"
)

# Initialize dashboard
dashboard = RealTimeDashboard(config)
dashboard.dashboard_config = dashboard_config

# Start monitoring
await dashboard.start_monitoring_async()

# Generate dashboard
dashboard_fig = await dashboard.generate_dashboard_async()
dashboard_fig.show()

# Get current metrics
metrics = dashboard.get_metrics_summary()
print(f"Active sessions: {metrics['active_sessions']}")
print(f"Recent alerts: {metrics['recent_alerts']}")
```

### Advanced Analytics

Leverage machine learning for pattern recognition:

```python
from cryptoforensics.visualization import AdvancedAnalytics

# Initialize analytics engine
analytics = AdvancedAnalytics(config)

# Detect behavioral patterns
patterns = await analytics.detect_behavioral_patterns_async(transactions)

for pattern in patterns:
    print(f"Pattern: {pattern.pattern_type}")
    print(f"Confidence: {pattern.confidence:.2f}")
    print(f"Risk Score: {pattern.risk_score:.2f}")
    print(f"Addresses: {len(pattern.addresses)}")
    print(f"Description: {pattern.description}")
    print()

# Detect anomalies
anomalies = await analytics.detect_anomalies_async(transactions)

for anomaly in anomalies:
    print(f"Anomaly: {anomaly.anomaly_type}")
    print(f"Severity: {anomaly.severity}")
    print(f"Confidence: {anomaly.confidence:.2f}")
    print(f"Explanation: {anomaly.explanation}")
    print()
```

## Security Features

### Enhanced Access Control

Implement role-based access control:

```python
# Check authorization for specific actions
authorized = await security.check_authorization_async(
    session_id=context.session_id,
    resource="investigations",
    action="create",
    data_classification="confidential"
)

if authorized:
    # Proceed with authorized action
    pass
else:
    print("Access denied")
```

### Data Encryption

Encrypt sensitive investigation data:

```python
# Encrypt sensitive data
sensitive_data = {
    "investigation_id": "case_001",
    "target_addresses": ["1Address1...", "1Address2..."],
    "findings": "Suspicious activity detected"
}

encrypted_package = await security.encrypt_sensitive_data_async(
    data=sensitive_data,
    classification="confidential"
)

print(f"Data encrypted with {encrypted_package['encryption_algorithm']}")
print(f"Classification: {encrypted_package['classification']}")
```

### Input Sanitization

Sanitize all user inputs for security:

```python
# Sanitize different types of input
clean_address = security.sanitize_input(
    "  **********************************  ",
    input_type="address"
)

clean_txid = security.sanitize_input(
    "a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890",
    input_type="txid"
)

clean_amount = security.sanitize_input("1.5", input_type="amount")
```

### Fault Tolerance

Monitor system health and enable automatic recovery:

```python
from cryptoforensics.security import FaultToleranceManager

# Initialize fault tolerance
fault_manager = FaultToleranceManager(config)

# Start system monitoring
await fault_manager.start_monitoring_async()

# Create backup
backup_info = await fault_manager.create_backup_async("full")
print(f"Backup created: {backup_info['backup_id']}")

# Check system status
status = fault_manager.get_system_status()
print(f"System health: {status['system_health']}")
print(f"Active circuit breakers: {status['circuit_breakers']}")
```

## Professional Investigation Tools

### Victim Fund Recovery

Manage victim fund recovery cases:

```python
from cryptoforensics.investigation import VictimRecoveryManager, RecoveryStatus

# Initialize recovery manager
recovery_manager = VictimRecoveryManager(config)

# Create recovery case
victim_info = {
    "victim_id": "victim_001",
    "victim_addresses": ["1VictimAddress123"]
}

incident_details = {
    "incident_date": "2023-01-01T10:00:00Z",
    "stolen_amount": 50.5,
    "currency": "BTC",
    "theft_addresses": ["1TheftAddress123", "1TheftAddress456"],
    "investigator": "detective_smith",
    "description": "Cryptocurrency theft via phishing attack"
}

# Create case
case = await recovery_manager.create_recovery_case_async(victim_info, incident_details)
print(f"Recovery case created: {case.case_id}")
print(f"Priority: {case.priority.value}")
print(f"Status: {case.status.value}")

# Monitor fund movement
transactions = get_recent_transactions()  # Your transaction source
alerts = await recovery_manager.monitor_fund_movement_async(transactions)

for alert in alerts:
    print(f"Alert: {alert.alert_type}")
    print(f"Amount moved: {alert.amount_moved} BTC")
    print(f"Priority: {alert.priority.value}")

# Coordinate with exchange
if alerts:
    request_package = await recovery_manager.coordinate_with_exchange_async(
        case_id=case.case_id,
        exchange_name="binance",
        request_type="freeze"
    )
    print("Exchange coordination initiated")

# Update case status
await recovery_manager.update_case_status_async(
    case.case_id,
    RecoveryStatus.FUNDS_LOCATED,
    "Funds located on exchange"
)
```

### Law Enforcement Integration

Integrate with law enforcement systems:

```python
from cryptoforensics.investigation import (
    LawEnforcementIntegration, CaseClassification, ReportType
)

# Initialize LE integration
le_integration = LawEnforcementIntegration(config, security)

# Create LE case
case_info = {
    "le_case_number": "FBI-2023-001",
    "agency_id": "fbi_cyber",
    "case_type": "cryptocurrency_fraud",
    "subject_addresses": ["1SuspectAddr123", "1SuspectAddr456"],
    "assigned_agents": ["agent_smith", "agent_jones"],
    "priority": "high"
}

le_case = await le_integration.create_le_case_async(case_info, context)
print(f"LE case created: {le_case.case_id}")
print(f"Classification: {le_case.classification.value}")

# Generate SAR report
sar_report = await le_integration.generate_le_report_async(
    case_id=le_case.case_id,
    report_type=ReportType.SAR,
    security_context=context
)

print(f"SAR report generated: {sar_report.report_id}")
print(f"Status: {sar_report.status}")

# Share intelligence
alert_info = {
    "alert_type": "threat_indicator",
    "priority": "high",
    "source_agency": "fbi_cyber",
    "subject_addresses": ["1ThreatAddr123"],
    "threat_indicators": ["large_transactions", "mixing_behavior"],
    "classification": "confidential"
}

intelligence_alert = await le_integration.share_intelligence_async(
    alert_info=alert_info,
    target_agencies=["secret_service", "finra"],
    security_context=context
)

print(f"Intelligence shared: {intelligence_alert.alert_id}")
```

## Advanced Anti-Evasion

### Enhanced Mixer Detection

Detect sophisticated mixing services:

```python
from cryptoforensics.analysis import MixerDetector

# Initialize mixer detector
mixer_detector = MixerDetector(config)

# Detect mixing activity
mixing_results = await mixer_detector.detect_mixing_async(transactions)

for result in mixing_results:
    print(f"Mixer type: {result.mixer_type}")
    print(f"Confidence: {result.confidence:.2f}")
    print(f"Addresses involved: {len(result.addresses)}")
    print(f"Mixing pattern: {result.pattern_description}")
```

### Cross-Chain Tracking

Track funds across multiple blockchains:

```python
from cryptoforensics.analysis import CrossChainTracker

# Initialize cross-chain tracker
tracker = CrossChainTracker(config)

# Track cross-chain movements
cross_chain_results = await tracker.track_cross_chain_async(
    source_address="1SourceAddr123",
    target_chains=["ethereum", "litecoin", "bitcoin_cash"]
)

for result in cross_chain_results:
    print(f"Chain: {result.target_chain}")
    print(f"Linked addresses: {result.linked_addresses}")
    print(f"Confidence: {result.confidence:.2f}")
```

## Best Practices

### Security Best Practices

1. **Always authenticate users** before accessing sensitive features
2. **Use appropriate data classifications** for different types of information
3. **Enable audit logging** for all operations
4. **Regularly backup** investigation data
5. **Monitor system health** and respond to alerts promptly

### Investigation Best Practices

1. **Start with 3D visualization** to understand transaction flows
2. **Use real-time monitoring** for ongoing investigations
3. **Apply machine learning analytics** to detect patterns
4. **Document all findings** with proper evidence collection
5. **Coordinate with relevant agencies** when appropriate

### Performance Best Practices

1. **Limit investigation depth** for large datasets
2. **Use incremental analysis** for real-time monitoring
3. **Enable caching** for frequently accessed data
4. **Monitor resource usage** and scale accordingly
5. **Use fault tolerance features** for critical investigations

## Troubleshooting

### Common Issues

#### Authentication Failures
```python
# Check user credentials and permissions
user_info = security.get_user_info("username")
if not user_info:
    print("User not found")

# Check rate limiting
if security._is_rate_limited("username", "*************"):
    print("Rate limited - wait before retrying")
```

#### Visualization Performance
```python
# Reduce dataset size for better performance
if len(transactions) > 1000:
    # Use sampling or filtering
    transactions = transactions[:1000]

# Disable animation for large datasets
viz_config.animation_enabled = False
```

#### Memory Issues
```python
# Monitor memory usage
memory_stats = fault_manager.performance_monitor.get_current_memory_usage()
if memory_stats["memory_percent"] > 80:
    # Trigger cleanup
    await fault_manager._recover_memory()
```

### Getting Help

- **Documentation**: Check the comprehensive documentation in `docs/`
- **Logs**: Review application logs for detailed error information
- **Support**: Contact professional support for enterprise customers
- **Community**: Join the CryptoForensics community for peer support

---

For more detailed information, see the [API Reference](api_reference.md) and [Developer Guide](developer_guide.md).
